<template>
  <div class="song-detail-test">
    <a-card title="歌曲详情 API 测试" style="margin: 20px;">
      <!-- 输入区域 -->
      <div class="input-section">
        <a-space direction="vertical" style="width: 100%;">
          <div>
            <label>歌曲ID（支持多个，用逗号分隔）：</label>
            <a-input 
              v-model:value="songIds" 
              placeholder="例如: 347230 或 347230,347231"
              style="margin-top: 8px;"
            />
          </div>
          
          <a-space>
            <a-button 
              type="primary" 
              @click="fetchSongDetails"
              :loading="loading"
            >
              获取歌曲详情
            </a-button>
            
            <a-button @click="clearResults">
              清空结果
            </a-button>
          </a-space>
        </a-space>
      </div>

      <!-- 结果展示区域 -->
      <div v-if="error" class="error-section">
        <a-alert 
          :message="error" 
          type="error" 
          show-icon 
          style="margin-top: 16px;"
        />
      </div>

      <div v-if="songDetails.length > 0" class="results-section">
        <a-divider>歌曲详情结果</a-divider>
        
        <div class="song-cards">
          <a-card 
            v-for="song in songDetails" 
            :key="song.id"
            class="song-card"
            :title="song.title"
          >
            <template #extra>
              <a-tag :color="song.privilege.canPlay ? 'green' : 'red'">
                {{ song.privilege.canPlay ? '可播放' : '无权限' }}
              </a-tag>
            </template>

            <div class="song-info">
              <!-- 封面和基本信息 -->
              <div class="song-basic">
                <img 
                  :src="song.coverUrl" 
                  :alt="song.title"
                  class="song-cover"
                  @error="handleImageError"
                />
                <div class="song-meta">
                  <p><strong>歌手：</strong>{{ song.artist }}</p>
                  <p><strong>专辑：</strong>{{ song.album }}</p>
                  <p><strong>时长：</strong>{{ song.durationText }}</p>
                  <p><strong>热度：</strong>{{ song.popularity }}</p>
                </div>
              </div>

              <!-- 音质信息 -->
              <div class="quality-info">
                <h4>可用音质：</h4>
                <a-space wrap>
                  <a-tag v-if="song.qualities.hr" color="purple">Hi-Res</a-tag>
                  <a-tag v-if="song.qualities.sq" color="gold">无损</a-tag>
                  <a-tag v-if="song.qualities.h" color="blue">高品质</a-tag>
                  <a-tag v-if="song.qualities.m" color="green">中品质</a-tag>
                  <a-tag v-if="song.qualities.l" color="default">低品质</a-tag>
                </a-space>
              </div>

              <!-- 权限信息 -->
              <div class="privilege-info">
                <h4>权限信息：</h4>
                <a-descriptions size="small" :column="2">
                  <a-descriptions-item label="可播放">
                    {{ song.privilege.canPlay ? '是' : '否' }}
                  </a-descriptions-item>
                  <a-descriptions-item label="可下载">
                    {{ song.privilege.canDownload ? '是' : '否' }}
                  </a-descriptions-item>
                  <a-descriptions-item label="最高码率">
                    {{ song.privilege.maxBitrate }}kbps
                  </a-descriptions-item>
                  <a-descriptions-item label="收费类型">
                    {{ getFeeTypeText(song.privilege.fee) }}
                  </a-descriptions-item>
                </a-descriptions>
              </div>

              <!-- 操作按钮 -->
              <div class="song-actions">
                <a-space>
                  <a-button 
                    type="primary" 
                    size="small"
                    :disabled="!song.privilege.canPlay"
                    @click="playSong(song)"
                  >
                    播放
                  </a-button>
                  <a-button 
                    size="small"
                    @click="viewRawData(song)"
                  >
                    查看原始数据
                  </a-button>
                </a-space>
              </div>
            </div>
          </a-card>
        </div>
      </div>

      <!-- 原始数据模态框 -->
      <a-modal
        v-model:open="rawDataVisible"
        title="原始数据"
        width="80%"
        :footer="null"
      >
        <pre class="raw-data">{{ JSON.stringify(selectedRawData, null, 2) }}</pre>
      </a-modal>
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { useMusicStore } from '../stores/music'

// Store
const musicStore = useMusicStore()

// 响应式数据
const songIds = ref('347230')
const loading = ref(false)
const error = ref('')
const songDetails = ref([])
const rawDataVisible = ref(false)
const selectedRawData = ref(null)

// 方法
async function fetchSongDetails() {
  if (!songIds.value.trim()) {
    message.warning('请输入歌曲ID')
    return
  }

  loading.value = true
  error.value = ''
  songDetails.value = []

  try {
    // 解析ID（支持逗号分隔的多个ID）
    const ids = songIds.value.split(',').map(id => id.trim()).filter(id => id)
    
    if (ids.length === 0) {
      throw new Error('请输入有效的歌曲ID')
    }

    // 调用API
    const result = await musicStore.fetchSongDetail(ids.length === 1 ? ids[0] : ids)
    
    // 处理结果
    songDetails.value = Array.isArray(result) ? result : [result]
    
    message.success(`成功获取 ${songDetails.value.length} 首歌曲详情`)
  } catch (err) {
    error.value = err.message || '获取歌曲详情失败'
    console.error('获取歌曲详情失败:', err)
  } finally {
    loading.value = false
  }
}

function clearResults() {
  songDetails.value = []
  error.value = ''
}

function playSong(song) {
  musicStore.playSong(song)
  message.success(`开始播放：${song.title}`)
}

function viewRawData(song) {
  selectedRawData.value = song.raw
  rawDataVisible.value = true
}

function getFeeTypeText(fee) {
  const feeTypes = {
    0: '免费',
    1: 'VIP歌曲',
    4: '购买专辑',
    8: '免费试听'
  }
  return feeTypes[fee] || `未知(${fee})`
}

function handleImageError(event) {
  event.target.src = '/images/default-cover.jpg'
}
</script>

<style lang="scss" scoped>
.song-detail-test {
  .input-section {
    margin-bottom: 20px;
    
    label {
      font-weight: 500;
      color: #333;
    }
  }

  .song-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 16px;
    margin-top: 16px;
  }

  .song-card {
    .song-info {
      .song-basic {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;

        .song-cover {
          width: 80px;
          height: 80px;
          border-radius: 8px;
          object-fit: cover;
        }

        .song-meta {
          flex: 1;
          
          p {
            margin: 4px 0;
            font-size: 14px;
          }
        }
      }

      .quality-info,
      .privilege-info {
        margin-bottom: 16px;

        h4 {
          margin-bottom: 8px;
          font-size: 14px;
          color: #666;
        }
      }

      .song-actions {
        padding-top: 12px;
        border-top: 1px solid #f0f0f0;
      }
    }
  }

  .raw-data {
    background: #f5f5f5;
    padding: 16px;
    border-radius: 4px;
    font-size: 12px;
    max-height: 500px;
    overflow-y: auto;
  }
}
</style>
