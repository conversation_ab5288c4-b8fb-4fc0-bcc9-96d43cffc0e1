<template>
  <div class="play-system-test">
    <a-card title="播放系统测试" style="margin: 20px;">
      <!-- 测试控制区域 -->
      <div class="test-controls">
        <a-space direction="vertical" style="width: 100%;" size="large">
          <!-- 单曲播放测试 -->
          <div class="test-section">
            <h3>单曲播放测试</h3>
            <a-space wrap>
              <a-button 
                type="primary" 
                @click="playSingleSong('347230')"
                :loading="loading"
              >
                播放《海阔天空》
              </a-button>
              <a-button 
                @click="playSingleSong('186016')"
                :loading="loading"
              >
                播放《十年》
              </a-button>
              <a-button 
                @click="playSingleSong('25906124')"
                :loading="loading"
              >
                播放《演员》
              </a-button>
            </a-space>
          </div>

          <!-- 播放列表测试 -->
          <div class="test-section">
            <h3>播放列表测试</h3>
            <a-space wrap>
              <a-button 
                type="primary" 
                @click="playTestPlaylist"
                :loading="loading"
              >
                播放测试歌单
              </a-button>
              <a-button 
                @click="clearPlaylist"
              >
                清空播放列表
              </a-button>
            </a-space>
          </div>

          <!-- 播放控制测试 -->
          <div class="test-section">
            <h3>播放控制测试</h3>
            <a-space wrap>
              <a-button 
                @click="togglePlay"
                :disabled="!musicStore.currentSong"
              >
                {{ musicStore.isPlaying ? '暂停' : '播放' }}
              </a-button>
              <a-button 
                @click="nextSong"
                :disabled="musicStore.playlist.length === 0"
              >
                下一首
              </a-button>
              <a-button 
                @click="prevSong"
                :disabled="musicStore.playlist.length === 0"
              >
                上一首
              </a-button>
            </a-space>
          </div>

          <!-- 音量控制测试 -->
          <div class="test-section">
            <h3>音量控制测试</h3>
            <a-space>
              <span>音量：</span>
              <a-slider 
                v-model:value="volume"
                @change="setVolume"
                :min="0"
                :max="100"
                style="width: 200px;"
              />
              <span>{{ volume }}%</span>
            </a-space>
          </div>
        </a-space>
      </div>

      <!-- 当前播放状态 -->
      <a-divider>当前播放状态</a-divider>
      <div class="current-status">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="当前歌曲">
            {{ musicStore.currentSong?.title || '无' }}
          </a-descriptions-item>
          <a-descriptions-item label="歌手">
            {{ musicStore.currentSong?.artist || '无' }}
          </a-descriptions-item>
          <a-descriptions-item label="播放状态">
            {{ musicStore.isPlaying ? '播放中' : '暂停' }}
          </a-descriptions-item>
          <a-descriptions-item label="加载状态">
            {{ musicStore.loading ? '加载中' : '就绪' }}
          </a-descriptions-item>
          <a-descriptions-item label="播放列表长度">
            {{ musicStore.playlist.length }}首
          </a-descriptions-item>
          <a-descriptions-item label="当前索引">
            {{ musicStore.currentIndex }}
          </a-descriptions-item>
          <a-descriptions-item label="播放模式">
            {{ getPlayModeText(musicStore.playMode) }}
          </a-descriptions-item>
          <a-descriptions-item label="音质">
            {{ musicStore.playQuality }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 错误信息 -->
        <div v-if="musicStore.error" class="error-info">
          <a-alert 
            :message="musicStore.error" 
            type="error" 
            show-icon 
            style="margin-top: 16px;"
          />
        </div>

        <!-- 当前歌曲详情 -->
        <div v-if="musicStore.currentSong" class="current-song-detail">
          <a-divider>当前歌曲详情</a-divider>
          <div class="song-info">
            <img 
              :src="musicStore.currentSong.coverUrl" 
              :alt="musicStore.currentSong.title"
              class="song-cover"
              @error="handleImageError"
            />
            <div class="song-meta">
              <h4>{{ musicStore.currentSong.title }}</h4>
              <p>歌手：{{ musicStore.currentSong.artist }}</p>
              <p>专辑：{{ musicStore.currentSong.album }}</p>
              <p>时长：{{ musicStore.currentSong.durationText }}</p>
              <p v-if="musicStore.currentSong.bitrate">码率：{{ musicStore.currentSong.bitrate }}kbps</p>
              <p v-if="musicStore.currentSong.audioUrl">
                播放链接：
                <a :href="musicStore.currentSong.audioUrl" target="_blank" class="audio-link">
                  查看链接
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 播放列表 -->
      <div v-if="musicStore.playlist.length > 0" class="playlist-section">
        <a-divider>当前播放列表</a-divider>
        <a-table 
          :dataSource="musicStore.playlist" 
          :columns="playlistColumns"
          :pagination="false"
          size="small"
          :scroll="{ y: 300 }"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'index'">
              <span :class="{ active: index === musicStore.currentIndex }">
                {{ index + 1 }}
              </span>
            </template>
            <template v-if="column.key === 'title'">
              <span 
                :class="{ active: index === musicStore.currentIndex }"
                @click="playFromPlaylist(index)"
                style="cursor: pointer;"
              >
                {{ record.title }}
              </span>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space>
                <a-button 
                  size="small" 
                  @click="playFromPlaylist(index)"
                  :type="index === musicStore.currentIndex ? 'primary' : 'default'"
                >
                  播放
                </a-button>
                <a-button 
                  size="small" 
                  danger
                  @click="removeFromPlaylist(index)"
                >
                  删除
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { useMusicStore } from '../stores/music'

// Store
const musicStore = useMusicStore()

// 响应式数据
const loading = ref(false)
const volume = ref(musicStore.volume)

// 播放列表表格列配置
const playlistColumns = [
  { title: '#', dataIndex: 'index', key: 'index', width: 50 },
  { title: '歌曲', dataIndex: 'title', key: 'title' },
  { title: '歌手', dataIndex: 'artist', key: 'artist' },
  { title: '时长', dataIndex: 'durationText', key: 'duration', width: 80 },
  { title: '操作', key: 'actions', width: 120 }
]

// 测试歌曲列表
const testSongs = [
  { id: '347230', title: '海阔天空', artist: 'Beyond' },
  { id: '186016', title: '十年', artist: '陈奕迅' },
  { id: '25906124', title: '演员', artist: '薛之谦' },
  { id: '33894312', title: '浮夸', artist: '陈奕迅' }
]

// 方法
async function playSingleSong(songId) {
  loading.value = true
  try {
    await musicStore.playSong(songId)
    message.success('开始播放')
  } catch (error) {
    message.error(error.message || '播放失败')
  } finally {
    loading.value = false
  }
}

async function playTestPlaylist() {
  loading.value = true
  try {
    // 获取测试歌曲的详细信息
    const songIds = testSongs.map(song => song.id)
    const songs = await musicStore.fetchSongDetail(songIds)
    
    // 播放歌单
    await musicStore.playPlaylist(songs, 0)
    message.success('开始播放测试歌单')
  } catch (error) {
    message.error(error.message || '播放歌单失败')
  } finally {
    loading.value = false
  }
}

function togglePlay() {
  musicStore.togglePlay()
}

async function nextSong() {
  try {
    await musicStore.nextSong()
  } catch (error) {
    message.error(error.message || '切换失败')
  }
}

async function prevSong() {
  try {
    await musicStore.prevSong()
  } catch (error) {
    message.error(error.message || '切换失败')
  }
}

function setVolume(value) {
  musicStore.setVolume(value)
}

function clearPlaylist() {
  musicStore.clearPlaylist()
  message.success('播放列表已清空')
}

async function playFromPlaylist(index) {
  try {
    const song = musicStore.playlist[index]
    if (song) {
      await musicStore.playSong(song)
    }
  } catch (error) {
    message.error(error.message || '播放失败')
  }
}

function removeFromPlaylist(index) {
  musicStore.removeSongFromPlaylist(index)
  message.success('已从播放列表移除')
}

function getPlayModeText(mode) {
  const modes = {
    order: '顺序播放',
    random: '随机播放',
    single: '单曲循环'
  }
  return modes[mode] || mode
}

function handleImageError(event) {
  event.target.src = '/images/default-cover.jpg'
}
</script>

<style lang="scss" scoped>
.play-system-test {
  .test-section {
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    
    h3 {
      margin-bottom: 12px;
      color: #333;
    }
  }

  .current-song-detail {
    .song-info {
      display: flex;
      gap: 16px;
      margin-top: 16px;

      .song-cover {
        width: 100px;
        height: 100px;
        border-radius: 8px;
        object-fit: cover;
      }

      .song-meta {
        flex: 1;
        
        h4 {
          margin-bottom: 8px;
          font-size: 16px;
        }
        
        p {
          margin: 4px 0;
          color: #666;
        }
        
        .audio-link {
          color: #1890ff;
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }

  .playlist-section {
    .active {
      color: #1890ff;
      font-weight: bold;
    }
  }
}
</style>
