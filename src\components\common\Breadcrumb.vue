<template>
  <div class="breadcrumb-container" v-if="breadcrumbItems.length > 1">
    <a-breadcrumb class="custom-breadcrumb">
      <a-breadcrumb-item 
        v-for="(item, index) in breadcrumbItems" 
        :key="index"
        :class="{ active: index === breadcrumbItems.length - 1 }"
      >
        <router-link 
          v-if="item.path && index !== breadcrumbItems.length - 1" 
          :to="item.path"
          class="breadcrumb-link"
        >
          <home-outlined v-if="index === 0" class="home-icon" />
          {{ item.title }}
        </router-link>
        <span v-else class="breadcrumb-current">
          <home-outlined v-if="index === 0" class="home-icon" />
          {{ item.title }}
        </span>
      </a-breadcrumb-item>
    </a-breadcrumb>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { HomeOutlined } from '@ant-design/icons-vue'

const route = useRoute()

// 从路由元信息中获取面包屑数据
const breadcrumbItems = computed(() => {
  return route.meta?.breadcrumb || []
})
</script>

<style lang="scss" scoped>
@import '../../styles/design-system.scss';

.breadcrumb-container {
  padding: $spacing-md $spacing-xl;
  background: rgba(255, 255, 255, 0.02);
  border-bottom: 1px solid var(--border-color);

  .custom-breadcrumb {
    :deep(.ant-breadcrumb-link) {
      color: var(--text-secondary);
      @include transition(color);

      &:hover {
        color: var(--primary-color);
      }
    }

    :deep(.ant-breadcrumb-separator) {
      color: var(--text-secondary);
      opacity: 0.5;
    }

    .breadcrumb-link {
      color: var(--text-secondary);
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 4px;
      @include transition(color);

      &:hover {
        color: var(--primary-color);
      }

      .home-icon {
        font-size: $font-size-sm;
      }
    }

    .breadcrumb-current {
      color: var(--text-primary);
      font-weight: $font-weight-medium;
      display: inline-flex;
      align-items: center;
      gap: 4px;

      .home-icon {
        font-size: $font-size-sm;
      }
    }

    :deep(.ant-breadcrumb-item) {
      &.active {
        .ant-breadcrumb-link {
          color: var(--text-primary);
          font-weight: $font-weight-medium;
        }
      }

      &:last-child {
        .ant-breadcrumb-link {
          color: var(--text-primary);
          font-weight: $font-weight-medium;
        }
      }
    }
  }
}
</style>
