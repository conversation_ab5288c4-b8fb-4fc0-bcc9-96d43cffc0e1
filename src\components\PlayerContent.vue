<template>
  <div class="player-content">
    <!-- 头部导航栏 -->
    <div class="content-header">
      <div class="navigation-buttons">
        <UiButton variant="text" size="small" class="nav-button">
          <left-outlined />
        </UiButton>
        <UiButton variant="text" size="small" class="nav-button">
          <right-outlined />
        </UiButton>
      </div>

      <div class="top-controls">
        <div class="search-box">
          <a-input
            placeholder="搜索音乐、视频、歌词"
            class="search-input"
            size="small"
            v-model:value="searchKeyword"
            @input="handleSearch"
            :loading="searchLoading"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
        </div>
      </div>

      <div class="user-info">
        <a-badge status="success" :offset="[8, 0]">
          <a-avatar class="user-avatar" size="small">
            <template #icon><user-outlined /></template>
          </a-avatar>
        </a-badge>
        <UiButton
          v-if="!currentUser"
          variant="text"
          size="small"
          @click="showLoginModal"
        >
          登录
        </UiButton>
        <a-dropdown v-else placement="bottomRight">
           <span class="user-name">
             {{ currentUser.username }}
             <down-outlined class="dropdown-arrow" />
           </span>
          <template #overlay>
            <a-menu>
              <a-menu-item key="profile">
                <user-outlined />
                个人资料
              </a-menu-item>
              <a-menu-item key="settings">
                <setting-outlined />
                设置
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item key="logout" @click="handleLogout">
                <logout-outlined />
                退出登录
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </div>

    <!-- 右上角按钮组 -->
    <div class="top-right-buttons">
      <!-- 爱心按钮 -->
      <div class="like-button">
        <UiButton
          variant="text"
          size="large"
          @click="toggleLike"
          class="heart-button"
        >
          <heart-filled v-if="currentSong?.liked" style="color: #ff4d4f" />
          <heart-outlined v-else />
        </UiButton>
        <span class="likes-count">10w+</span>
      </div>

      <!-- 全屏按钮 -->
      <div class="fullscreen-controls">
        <UiButton
          variant="text"
          size="large"
          @click="toggleBrowserFullscreen"
          class="fullscreen-button"
          :title="isBrowserFullscreen ? '退出全屏' : '进入全屏'"
        >
          <fullscreen-exit-outlined v-if="isBrowserFullscreen" />
          <fullscreen-outlined v-else />
        </UiButton>
      </div>

      <!-- 音量控制 -->
      <div class="volume-control" @mouseenter="showVolumeSlider" @mouseleave="hideVolumeSlider">
        <UiButton
          variant="text"
          size="large"
          @click="toggleMute"
          class="volume-button"
          :title="isMuted ? '取消静音' : '静音'"
        >
          <sound-filled v-if="!isMuted && volume > 50" />
          <sound-outlined v-else-if="!isMuted && volume > 0" />
          <span v-else class="muted-icon">
            <sound-outlined />
            <span class="mute-line"></span>
          </span>
        </UiButton>

        <!-- 音量滑块 -->
        <div class="volume-slider-container" :class="{ visible: volumeSliderVisible }">
          <a-slider
            v-model:value="volume"
            :min="0"
            :max="100"
            :step="1"
            vertical
            class="volume-slider"
            :tooltip-visible="false"
            @change="handleVolumeChange"
          />
          <div class="volume-percentage">{{ volume }}%</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-main" @click="toggleFullscreenLyrics">
      <!-- 全屏歌词模式 -->
      <div v-if="isFullscreenLyrics" class="fullscreen-lyrics">
        <div class="fullscreen-lyrics-content">
          <!-- 顶部控制栏 -->
          <div class="fullscreen-controls-bar">
            <!-- 模式切换按钮 -->
            <div class="mode-toggle">
              <UiButton
                variant="text"
                size="medium"
                @click.stop="toggleFullscreenMode"
                class="mode-btn"
                :title="fullscreenMode === 'lyrics' ? '显示封面' : '只看歌词'"
              >
                <picture-outlined v-if="fullscreenMode === 'lyrics'" />
                <file-text-outlined v-else />
                <span class="mode-text">
                  {{ fullscreenMode === 'lyrics' ? '显示封面' : '只看歌词' }}
                </span>
              </UiButton>
            </div>

            <!-- 返回按钮 -->
            <div class="back-button">
              <UiButton
                variant="text"
                size="medium"
                @click.stop="toggleFullscreenLyrics"
                class="back-btn"
                title="返回"
              >
                <left-outlined />
                <span class="back-text">返回</span>
              </UiButton>
            </div>
          </div>

          <!-- 双模式内容区域 -->
          <div class="fullscreen-main" :class="`mode-${fullscreenMode}`">
            <!-- 封面+歌词模式 -->
            <div v-if="fullscreenMode === 'cover'" class="cover-lyrics-mode">
              <div class="cover-section">
                <div
                  class="fullscreen-album-cover"
                  :class="{ rotating: isPlaying }"
                  :key="`fullscreen-album-cover-${animationKey}`"
                  @click.stop="togglePlayPause"
                >
                  <img :src="albumCoverUrl" alt="Album Cover" @error="handleImageError" />
                  <div class="vinyl-effect">
                    <div class="vinyl-center"></div>
                  </div>

                  <!-- 全屏模式播放/暂停悬浮按钮 -->
                  <div class="fullscreen-play-overlay" :class="{ playing: isPlaying }">
                    <div class="fullscreen-play-button">
                      <pause-outlined v-if="isPlaying" />
                      <caret-right-outlined v-else />
                    </div>
                  </div>
                </div>

                <div class="song-info-fullscreen">
                  <h2 class="song-title">{{ currentSong?.title || 'Summer melody(温柔)' }}</h2>
                  <p class="song-artist">{{ currentSong?.artist || 'FLYSBR' }}</p>
                </div>
              </div>

              <div class="lyrics-section">
                <div class="current-lyrics">
                  <div class="current-line">{{ fullLyrics[currentLyricIndex]?.text || currentLyric }}</div>
                  <div class="next-line">{{ fullLyrics[currentLyricIndex + 1]?.text || secondLyric }}</div>
                </div>
              </div>
            </div>

            <!-- 纯歌词模式 -->
            <div v-else class="lyrics-only-mode">
              <div class="song-header">
                <h2 class="song-title">{{ currentSong?.title || 'Summer melody(温柔)' }}</h2>
                <p class="song-artist">{{ currentSong?.artist || 'FLYSBR' }}</p>
              </div>

              <div class="lyrics-list" ref="lyricsListRef">
                <div
                  v-for="(lyric, index) in fullLyrics"
                  :key="index"
                  class="lyric-line"
                  :class="{
                    active: index === currentLyricIndex,
                    passed: index < currentLyricIndex
                  }"
                  :ref="el => { if (index === currentLyricIndex) activeLyricRef = el }"
                >
                  {{ lyric.text }}
                </div>
              </div>
            </div>
          </div>

          <!-- 提示文字 -->
          <div class="hint-text">
            <p>点击任意位置返回播放页面</p>
          </div>
        </div>
      </div>

      <!-- 普通播放模式 -->
      <div v-else>
        <!-- 加载状态 -->
        <div v-if="contentLoading" class="loading-container">
          <UiSkeleton class="skeleton-player-content">
            <div class="skeleton-album-cover"></div>
            <div class="skeleton-song-info">
              <div class="skeleton-title"></div>
              <div class="skeleton-artist"></div>
            </div>
            <div class="skeleton-lyrics">
              <div class="skeleton-line"></div>
              <div class="skeleton-line"></div>
            </div>
          </UiSkeleton>
        </div>

        <!-- 歌曲展示 -->
        <div v-else class="song-display">
          <div
            class="album-cover"
            :class="{ rotating: isPlaying }"
            :key="`album-cover-${animationKey}`"
            @click.stop="togglePlayPause"
          >
            <img :src="albumCoverUrl" alt="Album Cover" @error="handleImageError" />
            <div class="vinyl-effect">
              <div class="vinyl-center"></div>
            </div>

            <!-- 播放/暂停悬浮按钮 -->
            <div class="play-overlay" :class="{ playing: isPlaying }">
              <div class="play-button">
                <pause-outlined v-if="isPlaying" />
                <caret-right-outlined v-else />
              </div>
            </div>
          </div>

          <div class="song-info">
            <h2 class="song-title">{{ currentSong?.title || 'Summer melody(温柔)' }}</h2>
            <p class="song-artist">{{ currentSong?.artist || 'FLYSBR' }}</p>

            <!-- 歌曲标签 -->
            <div v-if="currentSong?.tags" class="song-tags">
              <span v-for="tag in currentSong.tags" :key="tag" class="tag">
                {{ tag }}
              </span>
            </div>

            <!-- 歌词显示 - 移到歌曲信息下方 -->
            <div class="lyrics-container">
              <div class="lyrics-line first-line">
                <p>{{ currentLyric }}</p>
              </div>

              <div class="lyrics-line second-line">
                <p>{{ secondLyric }}</p>
              </div>

              <!-- 点击提示 -->
              <div class="lyrics-hint">
                <p>点击页面查看完整歌词</p>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>



    <LoginModal :visible="loginModalVisible" @close="closeLoginModal" @login-success="handleLoginSuccess" />
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useMusicStore } from '../stores/music';
import {
  LeftOutlined,
  RightOutlined,
  UserOutlined,
  HeartOutlined,
  HeartFilled,
  SearchOutlined,
  SettingOutlined,
  LogoutOutlined,
  DownOutlined,

  FullscreenOutlined,
  FullscreenExitOutlined,
  PictureOutlined,
  FileTextOutlined,
  CaretRightOutlined,
  PauseOutlined,
  SoundOutlined,
  SoundFilled
} from '@ant-design/icons-vue';
import LoginModal from './LoginModal.vue';
import UiButton from './ui/UiButton.vue';
import UiSkeleton from './ui/UiSkeleton.vue';

// 导入图片资源
import previewImage from '../assets/preview.jpg';

const musicStore = useMusicStore();

// 定义事件
const emit = defineEmits(['change-view', 'login-success', 'logout']);

// 接收属性
const props = defineProps({
  currentUser: {
    type: Object,
    default: null
  },
  isLoggedIn: {
    type: Boolean,
    default: false
  }
});

// --------------------播放器内容区域功能--------------------
const currentSong = computed(() => musicStore.currentSong || musicStore.playlist[0]);
const isPlaying = computed(() => musicStore.isPlaying);

// 加载状态
const contentLoading = ref(false);
const searchLoading = ref(false);

// 全屏歌词状态
const isFullscreenLyrics = ref(false);
const fullscreenMode = ref('cover'); // 'cover' | 'lyrics'

// 歌词滚动相关
const lyricsListRef = ref(null);
const activeLyricRef = ref(null);

// 音频播放时间状态
const currentTime = ref(0);
const duration = ref(0);
const audioElement = ref(null);

// 浏览器全屏状态
const isBrowserFullscreen = ref(false);

// 音量控制状态
const volume = ref(80); // 默认音量80%
const previousVolume = ref(80); // 保存静音前的音量
const isMuted = ref(false);
const volumeSliderVisible = ref(false);
const volumeSliderTimer = ref(null);

// 封面图片URL - 动态获取当前歌曲封面
const albumCoverUrl = computed(() => {
  if (!currentSong.value) return previewImage;

  // 尝试多种可能的封面图片路径
  const song = currentSong.value;
  const coverUrl = song.coverUrl ||
                   song.al?.picUrl ||
                   song.album?.picUrl ||
                   song.raw?.al?.picUrl ||  // 从原始数据获取
                   song.raw?.album?.picUrl ||
                   song.picUrl ||  // 直接的图片URL
                   song.cover ||   // 其他可能的字段名
                   previewImage;

  // 调试信息
  if (coverUrl === previewImage) {
    console.log('使用默认封面，当前歌曲数据:', song);
  }

  return coverUrl;
});
const defaultCover = previewImage;

// 动画重置控制
const animationKey = ref(0);

// 歌词数据
const fullLyrics = computed(() => {
  return musicStore.currentLyrics?.parsedLyrics || [
    { text: '夏日的风 吹动着头发', time: 0 },
    { text: '温柔的晚霞 映照着她', time: 5 },
    { text: '点击页面查看完整歌词', time: 10 }
  ];
});

// 根据当前播放时间计算当前歌词索引
const currentLyricIndex = computed(() => {
  const lyrics = fullLyrics.value;
  if (!lyrics.length) return 0;

  // 找到当前时间对应的歌词索引
  let index = 0;
  for (let i = 0; i < lyrics.length; i++) {
    if (currentTime.value >= lyrics[i].time) {
      index = i;
    } else {
      break;
    }
  }
  return index;
});

// 当前显示的歌词
const currentLyric = computed(() => {
  const lyrics = fullLyrics.value;
  const index = currentLyricIndex.value;

  if (lyrics.length > 0 && lyrics[index]) {
    return lyrics[index].text || '♪';
  }
  return '夏日的风 吹动着头发';
});

// 第二行歌词（下一句）
const secondLyric = computed(() => {
  const lyrics = fullLyrics.value;
  const nextIndex = currentLyricIndex.value + 1;

  if (lyrics.length > nextIndex && lyrics[nextIndex]) {
    return lyrics[nextIndex].text || '♪';
  }
  return '温柔的晚霞 映照着她';
});

function toggleLike() {
  if (currentSong.value) {
    musicStore.toggleLike(currentSong.value.id);
  }
}

function handleImageError(event) {
  event.target.src = defaultCover;
}

// 切换浏览器全屏
async function toggleBrowserFullscreen() {
  try {
    if (!document.fullscreenElement) {
      await document.documentElement.requestFullscreen();
      isBrowserFullscreen.value = true;
    } else {
      await document.exitFullscreen();
      isBrowserFullscreen.value = false;
    }
  } catch (error) {
    console.error('全屏切换失败:', error);
  }
}

// 切换全屏歌词
function toggleFullscreenLyrics() {
  isFullscreenLyrics.value = !isFullscreenLyrics.value;

  // 进入全屏歌词模式时，延迟滚动到当前歌词
  if (isFullscreenLyrics.value) {
    setTimeout(() => {
      scrollToActiveLyric();
    }, 100);
  }
}

// 切换全屏模式（封面+歌词 / 纯歌词）
function toggleFullscreenMode() {
  fullscreenMode.value = fullscreenMode.value === 'cover' ? 'lyrics' : 'cover';

  // 切换到纯歌词模式时，延迟滚动到当前歌词
  if (fullscreenMode.value === 'lyrics') {
    setTimeout(() => {
      scrollToActiveLyric();
    }, 100);
  }
}

// 切换播放/暂停
function togglePlayPause() {
  musicStore.togglePlay();
}

// 音量控制功能
function toggleMute() {
  if (isMuted.value) {
    // 取消静音：恢复之前的音量
    isMuted.value = false;
    volume.value = previousVolume.value;
    musicStore.setVolume(previousVolume.value);
  } else {
    // 静音：保存当前音量，设置为0
    previousVolume.value = volume.value;
    isMuted.value = true;
    volume.value = 0;
    musicStore.setVolume(0);
  }
}

function handleVolumeChange(newVolume) {
  volume.value = newVolume;
  if (newVolume === 0) {
    isMuted.value = true;
  } else {
    isMuted.value = false;
    // 如果不是静音状态，更新 previousVolume
    if (!isMuted.value) {
      previousVolume.value = newVolume;
    }
  }
  musicStore.setVolume(newVolume);
}

function showVolumeSlider() {
  volumeSliderVisible.value = true;
  // 清除之前的定时器
  if (volumeSliderTimer.value) {
    clearTimeout(volumeSliderTimer.value);
  }
}

function hideVolumeSlider() {
  // 延迟隐藏，给用户时间操作滑块
  volumeSliderTimer.value = setTimeout(() => {
    volumeSliderVisible.value = false;
  }, 300);
}



// 监听歌曲变化，重置动画并获取歌词
watch(() => musicStore.currentSong, (newSong, oldSong) => {
  if (newSong && oldSong && newSong.id !== oldSong.id) {
    // 歌曲切换时重置动画
    resetAnimation();
    // 获取新歌曲的歌词
    fetchCurrentSongLyrics();
  } else if (newSong && !oldSong) {
    // 首次播放歌曲时获取歌词
    fetchCurrentSongLyrics();
  }
}, { deep: true });

// 监听当前歌词索引变化，自动滚动到中间位置
watch(() => currentLyricIndex.value, () => {
  if (isFullscreenLyrics.value && fullscreenMode.value === 'lyrics') {
    nextTick(() => {
      scrollToActiveLyric();
    });
  }
});

// 重置动画函数
function resetAnimation() {
  // 通过改变key值强制重新渲染动画
  animationKey.value++;
}

// --------------------登录功能--------------------
const loginModalVisible = ref(false);

// 组件挂载时的初始化
onMounted(() => {
  // 监听浏览器全屏状态变化
  document.addEventListener('fullscreenchange', handleFullscreenChange);

  // 检查localStorage中是否有保存的用户信息
  const savedUser = localStorage.getItem('currentUser');
  if (savedUser && !props.isLoggedIn) {
    try {
      const userInfo = JSON.parse(savedUser);
      emit('login-success', userInfo);
    } catch (e) {
      console.error('Failed to parse saved user:', e);
    }
  }

  // 如果已有当前歌曲，获取歌词
  if (currentSong.value && currentSong.value.id) {
    fetchCurrentSongLyrics();
  }

  // 设置音频时间监听
  setupAudioTimeListener();
});

// 处理浏览器全屏状态变化
function handleFullscreenChange() {
  isBrowserFullscreen.value = !!document.fullscreenElement;
}

function showLoginModal() {
  loginModalVisible.value = true;
}

function closeLoginModal() {
  loginModalVisible.value = false;
}

// 处理登录成功
function handleLoginSuccess(userInfo) {
  closeLoginModal();
  emit('login-success', userInfo);
  
  // 保存到localStorage
  localStorage.setItem('currentUser', JSON.stringify(userInfo));
}

// 处理退出登录
function handleLogout() {
  emit('logout');
  
  // 清除localStorage中的用户信息
  localStorage.removeItem('currentUser');
}

// 搜索功能
const searchKeyword = ref('');
const searchResults = ref([]);

// 处理搜索
async function handleSearch(e) {
  const keyword = e.target.value;
  if (!keyword) {
    searchResults.value = [];
    searchLoading.value = false;
    return;
  }

  searchLoading.value = true;

  // 模拟异步搜索，实际应用中可连接到后端API
  try {
    await new Promise(resolve => setTimeout(resolve, 500)); // 模拟网络延迟

    searchResults.value = musicStore.playlist.filter(song => {
      return song.title.toLowerCase().includes(keyword.toLowerCase()) ||
             song.artist.toLowerCase().includes(keyword.toLowerCase()) ||
             (song.album && song.album.toLowerCase().includes(keyword.toLowerCase()));
    });

    console.log('搜索关键词:', keyword);
    console.log('搜索结果:', searchResults.value);
  } catch (error) {
    console.error('搜索失败:', error);
  } finally {
    searchLoading.value = false;
  }
}

// --------------------歌词功能--------------------
// 获取歌词数据
async function fetchCurrentSongLyrics() {
  if (currentSong.value && currentSong.value.id) {
    try {
      await musicStore.fetchLyrics(currentSong.value.id);
    } catch (error) {
      console.error('获取歌词失败:', error);
    }
  }
}

// 设置音频时间监听
function setupAudioTimeListener() {
  // 从全局音频播放器获取音频元素
  const globalAudio = document.querySelector('audio');
  if (globalAudio) {
    audioElement.value = globalAudio;

    // 监听时间更新
    const handleTimeUpdate = () => {
      currentTime.value = globalAudio.currentTime;
    };

    // 监听时长加载
    const handleLoadedMetadata = () => {
      duration.value = globalAudio.duration;
    };

    // 添加事件监听器
    globalAudio.addEventListener('timeupdate', handleTimeUpdate);
    globalAudio.addEventListener('loadedmetadata', handleLoadedMetadata);

    // 组件卸载时清理监听器
    onUnmounted(() => {
      if (globalAudio) {
        globalAudio.removeEventListener('timeupdate', handleTimeUpdate);
        globalAudio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      }
    });
  } else {
    // 如果没有找到音频元素，延迟重试
    setTimeout(setupAudioTimeListener, 1000);
  }
}

// 滚动到当前歌词位置
function scrollToActiveLyric() {
  if (!lyricsListRef.value) return;

  const container = lyricsListRef.value;
  const activeIndex = currentLyricIndex.value;
  const lyricLines = container.querySelectorAll('.lyric-line');

  if (lyricLines[activeIndex]) {
    const activeLine = lyricLines[activeIndex];
    const containerHeight = container.clientHeight;
    const lineTop = activeLine.offsetTop;
    const lineHeight = activeLine.offsetHeight;

    // 计算滚动位置，让当前歌词显示在容器中间
    const targetScrollTop = lineTop - (containerHeight / 2) + (lineHeight / 2);

    // 平滑滚动到目标位置
    container.scrollTo({
      top: Math.max(0, targetScrollTop),
      behavior: 'smooth'
    });
  }
}
</script>

<style lang="scss" scoped>
@import '../styles/design-system.scss';

.player-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  overflow: hidden;
  position: relative;

  // 右上角按钮组
  .top-right-buttons {
    position: absolute;
    top: 70px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
    z-index: 10;

    .like-button {
      display: flex;
      flex-direction: column;
      align-items: center;

      .heart-button {
        font-size: 24px;
        color: var(--text-primary);
        @include transition();

        &:hover {
          transform: scale(1.1);
        }
      }

      .likes-count {
        color: var(--text-secondary);
        font-size: $font-size-xs;
        margin-top: $spacing-xs;
      }
    }

    .fullscreen-controls {
      display: flex;
      justify-content: center;

      .fullscreen-button {
        font-size: 20px;
        color: var(--text-primary);
        @include transition();

        &:hover {
          color: var(--primary-color);
          transform: scale(1.1);
        }
      }
    }

    .volume-control {
      position: relative;
      display: flex;
      align-items: center;

      .volume-button {
        font-size: 20px;
        color: var(--text-primary);
        @include transition();

        &:hover {
          color: var(--primary-color);
          transform: scale(1.1);
        }

        .muted-icon {
          position: relative;
          display: inline-block;

          .mute-line {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 2px;
            background: #ff4d4f;
            transform: translate(-50%, -50%) rotate(-45deg);
            border-radius: 1px;
            box-shadow: 0 0 4px rgba(255, 77, 79, 0.5);
          }
        }
      }

      .volume-slider-container {
        position: absolute;
        right: 100%;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(0, 0, 0, 0.85); // 改为黑色背景
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: $border-radius-lg;
        padding: $spacing-lg $spacing-md;
        margin-right: $spacing-sm;
        opacity: 0;
        visibility: hidden;
        @include transition(all);
        box-shadow:
          0 4px 20px rgba(0, 0, 0, 0.5),
          0 2px 10px rgba(0, 0, 0, 0.3);
        min-height: 140px; // 稍微增加高度
        width: 60px; // 明确设置宽度
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: $spacing-sm;

        &.visible {
          opacity: 1;
          visibility: visible;
          transform: translateY(-50%) translateX(-8px);
        }

        .volume-slider {
          height: 100px;
          margin: 0;
          width: 40px; // 增加宽度以填充黑色方框
          transform: rotate(180deg); // 翻转滑块，让进度条从底部开始

          :deep(.ant-slider) {
            width: 40px; // 确保整个滑块区域填充容器
            padding: 0 14px; // 增加内边距，扩大可点击区域
          }

          :deep(.ant-slider-rail) {
            background: linear-gradient(0deg,
              rgba(255, 255, 255, 0.1),
              rgba(255, 255, 255, 0.15),
              rgba(255, 255, 255, 0.1)
            );
            width: 12px; // 增加轨道宽度
            border-radius: 6px;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
            left: 50%;
            transform: translateX(-50%);
          }

          :deep(.ant-slider-track) {
            background: linear-gradient(180deg,
              #ff6b6b 0%,
              #ec4141 50%,
              #d63031 100%
            );
            width: 12px; // 增加进度条宽度，与轨道保持一致
            border-radius: 6px;
            box-shadow:
              0 0 12px rgba(236, 65, 65, 0.5),
              0 2px 6px rgba(236, 65, 65, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            left: 50%;
            transform: translateX(-50%);

            &::after {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(180deg,
                rgba(255, 255, 255, 0.3),
                transparent 30%,
                transparent 70%,
                rgba(0, 0, 0, 0.1)
              );
              border-radius: 6px;
            }
          }

          // 移除滑块手柄（小白点）
          :deep(.ant-slider-handle) {
            display: none !important;
          }

          // 禁用滑块位置变化的动画，支持点击跳转
          :deep(.ant-slider-track) {
            transition-property: box-shadow !important;
            transition-duration: 0.2s !important;
          }

          // 点击时立即跳转
          &:active :deep(.ant-slider-track) {
            transition: none !important;
          }

          // 增加悬停区域
          &:hover :deep(.ant-slider-track) {
            box-shadow:
              0 0 16px rgba(236, 65, 65, 0.7),
              0 3px 8px rgba(236, 65, 65, 0.4),
              inset 0 1px 0 rgba(255, 255, 255, 0.3);
          }
        }

        .volume-percentage {
          color: var(--text-secondary);
          font-size: $font-size-xs;
          font-weight: $font-weight-medium;
          text-align: center;
          min-width: 30px;
        }
      }
    }
  }

  // 头部导航
  .content-header {
    height: var(--header-height);
    display: flex;
    align-items: center;
    padding: 0 $spacing-md;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
    backdrop-filter: blur(10px);

    // 禁止文字选中和图片拖拽
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    // 禁止所有元素拖拽
    * {
        -webkit-user-drag: none;
        -khtml-user-drag: none;
        -moz-user-drag: none;
        -o-user-drag: none;
        pointer-events: auto; // 保持点击事件
    }

    // 特别针对图片禁止拖拽
    img {
        -webkit-user-drag: none;
        -khtml-user-drag: none;
        -moz-user-drag: none;
        -o-user-drag: none;
        pointer-events: none; // 图片完全禁止交互
    }

    .navigation-buttons {
      display: flex;
      gap: $spacing-sm;
      margin-right: $spacing-md;

      .nav-button {
        color: var(--text-primary);
        font-size: $font-size-md;

        &:hover {
          color: var(--primary-color);
        }
      }
    }

    .top-controls {
      flex: 1;
      display: flex;
      justify-content: flex-start;
    }

    .search-box {
      flex: 1;
      display: flex;
      justify-content: flex-start;
      max-width: 300px;

      .search-input {
        width: 100%;
        border-radius: 50px;
        background: rgba(255, 255, 255, 0.08);
        border: 1px solid transparent;padding: 5px;
        @include transition();

        &:hover {
          background: rgba(255, 255, 255, 0.12);
          border-color: var(--border-hover);
        }

        &:focus-within {
          background: rgba(255, 255, 255, 0.15);
          border-color: var(--primary-color);
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        :deep(.ant-input) {
          background: transparent;
          color: var(--text-primary);
          font-size: $font-size-sm;
          border: none;
          padding-left: $spacing-xs;

          &::placeholder {
            color: var(--text-disabled);
          }
        }

        :deep(.ant-input-prefix) {
          margin-right: $spacing-xs;
          color: var(--text-disabled);
        }
      }
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      margin-left: auto;

      .user-avatar {
        background: var(--bg-card);
        color: var(--text-primary);
        border: 1px solid var(--border-color);
      }

      .user-name {
         color: var(--text-primary);
         font-size: $font-size-sm;
         cursor: pointer;
         display: flex;
         align-items: center;
         gap: $spacing-xs;
         padding: $spacing-xs $spacing-sm;
         border-radius: $border-radius-sm;
         @include transition();

         &:hover {
           background: rgba(255, 255, 255, 0.1);
         }

         .dropdown-arrow {
           font-size: $font-size-xs;
           @include transition(transform);
         }
       }
    }
  }

  // 主要内容区域
  .content-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xxl;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    @include transition();

    // 禁止文字选中和图片拖拽
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    // 禁止所有元素拖拽
    * {
        -webkit-user-drag: none;
        -khtml-user-drag: none;
        -moz-user-drag: none;
        -o-user-drag: none;
        pointer-events: auto; // 保持点击事件
    }

    // 特别针对图片禁止拖拽
    img {
        -webkit-user-drag: none;
        -khtml-user-drag: none;
        -moz-user-drag: none;
        -o-user-drag: none;
        pointer-events: none; // 图片完全禁止交互
    }

    // 全屏歌词模式
    .fullscreen-lyrics {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: fadeIn 0.3s ease-in-out;

      .fullscreen-lyrics-content {
        width: 100%;
        max-width: 800px;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: $spacing-xl;
        position: relative;

        .fullscreen-controls-bar {
          position: fixed;
          top: 20px;
          right: 20px;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          gap: $spacing-md;
          z-index: 1000;

          .back-button {
            .back-btn {
              color: var(--text-primary);

              &:hover {
                color: var(--primary-color);
              }

              .back-text {
                margin-left: $spacing-xs;
              }
            }
          }

          .mode-toggle {
            .mode-btn {
              color: var(--text-primary);
              display: flex;
              align-items: center;
              gap: $spacing-xs;

              .mode-text {
                font-size: $font-size-sm;
                color: var(--text-secondary);
              }

              &:hover {
                color: var(--primary-color);

                .mode-text {
                  color: var(--primary-color);
                }
              }
            }
          }
        }

        .fullscreen-main {
          flex: 1;
          display: flex;
          flex-direction: column;
          padding-top: $spacing-xxl;

          // 封面+歌词模式
          &.mode-cover {
            .cover-lyrics-mode {
              display: flex;
              height: 100%;
              gap: $spacing-xxl;

              .cover-section {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .fullscreen-album-cover {
                  position: relative;
                  width: 400px;
                  height: 400px;
                  border-radius: 50%;
                  overflow: hidden;
                  box-shadow: var(--shadow-xl);
                  border: 6px solid rgba(255, 255, 255, 0.1);
                  @include transition();
                  cursor: pointer;

                  // 始终应用动画，但根据状态控制播放
                  animation: rotate 20s linear infinite;
                  animation-play-state: paused;

                  &.rotating {
                    animation-play-state: running;
                  }

                  img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                  }

                  // 全屏模式播放/暂停悬浮按钮
                  .fullscreen-play-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.6);
                    @include flex-center;
                    opacity: 0;
                    @include transition();

                    .fullscreen-play-button {
                      width: 80px;
                      height: 80px;
                      background: var(--primary-color);
                      border-radius: 50%;
                      @include flex-center;
                      color: white;
                      font-size: 32px;
                      @include transition();
                      box-shadow: 0 6px 24px rgba(24, 144, 255, 0.4);

                      &:hover {
                        transform: scale(1.1);
                        background: var(--primary-hover);
                        box-shadow: 0 8px 32px rgba(24, 144, 255, 0.6);
                      }
                    }
                  }

                  &:hover .fullscreen-play-overlay {
                    opacity: 1;
                  }

                  .fullscreen-play-overlay.playing {
                    opacity: 0;
                  }

                  .vinyl-effect {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: radial-gradient(
                      circle at center,
                      transparent 30%,
                      rgba(0, 0, 0, 0.1) 31%,
                      rgba(0, 0, 0, 0.1) 32%,
                      transparent 33%
                    );
                    pointer-events: none;

                    .vinyl-center {
                      position: absolute;
                      top: 50%;
                      left: 50%;
                      transform: translate(-50%, -50%);
                      width: 80px;
                      height: 80px;
                      background: var(--bg-primary);
                      border-radius: 50%;
                      border: 3px solid rgba(255, 255, 255, 0.2);

                      &::after {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        width: 30px;
                        height: 30px;
                        background: var(--bg-secondary);
                        border-radius: 50%;
                      }
                    }
                  }
                }

                .song-info-fullscreen {
                  text-align: center;
                  margin-top: $spacing-xl;

                  .song-title {
                    font-size: $font-size-xxxl;
                    font-weight: $font-weight-bold;
                    color: var(--text-primary);
                    margin: 0 0 $spacing-sm 0;
                  }

                  .song-artist {
                    font-size: $font-size-xl;
                    color: var(--text-secondary);
                    margin: 0;
                  }
                }
              }

              .lyrics-section {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;

                .current-lyrics {
                  text-align: center;
                  max-width: 600px;

                  .current-line {
                    font-size: 2.5rem;
                    font-weight: $font-weight-bold;
                    color: var(--primary-color);
                    margin-bottom: $spacing-xl;
                    line-height: 1.3;
                    text-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                  }

                  .next-line {
                    font-size: 1.8rem;
                    color: var(--text-secondary);
                    opacity: 0.8;
                    line-height: 1.4;
                  }
                }
              }
            }
          }

          // 纯歌词模式
          &.mode-lyrics {
            .lyrics-only-mode {
              height: 100%;
              display: flex;
              flex-direction: column;

              .song-header {
                text-align: center;
                margin-bottom: $spacing-xl;

                .song-title {
                  font-size: $font-size-xxxl;
                  font-weight: $font-weight-bold;
                  color: var(--text-primary);
                  margin: 0 0 $spacing-sm 0;
                }

                .song-artist {
                  font-size: $font-size-xl;
                  color: var(--text-secondary);
                  margin: 0;
                }
              }

              .lyrics-list {
                flex: 1;
                overflow-y: auto;
                padding: $spacing-lg 0;
                max-height: 60vh; // 限制最大高度为视口高度的60%
                @include custom-scrollbar();

                .lyric-line {
                  text-align: center;
                  padding: $spacing-lg 0;
                  font-size: $font-size-xl;
                  line-height: 1.6;
                  color: var(--text-disabled);
                  @include transition();
                  margin: 0 auto;
                  max-width: 600px;

                  &.active {
                    color: var(--primary-color);
                    font-weight: $font-weight-bold;
                    font-size: $font-size-xxl;
                    transform: scale(1.05);
                  }

                  &.passed {
                    color: var(--text-secondary);
                    opacity: 0.7;
                  }
                }
              }
            }
          }
        }



        .hint-text {
          text-align: center;
          padding: $spacing-lg 0;

          p {
            color: var(--text-disabled);
            font-size: $font-size-sm;
            margin: 0;
            opacity: 0.8;
          }
        }
      }
    }

    .loading-container {
      width: 100%;
      max-width: 400px;
    }

    .song-display {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: $spacing-lg;

      .album-cover {
        position: relative;
        width: 300px;
        height: 300px;
        border-radius: 50%; // 改为圆形
        overflow: hidden;
        box-shadow: var(--shadow-xl);
        @include transition();
        border: 4px solid rgba(255, 255, 255, 0.1); // 添加边框
        cursor: pointer;

        // 始终应用动画，但根据状态控制播放
        animation: rotate 20s linear infinite;
        animation-play-state: paused;

        &.rotating {
          animation-play-state: running;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        // 播放/暂停悬浮按钮
        .play-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.6);
          @include flex-center;
          opacity: 0;
          @include transition();
          cursor: pointer;

          .play-button {
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            border-radius: 50%;
            @include flex-center;
            color: white;
            font-size: 24px;
            @include transition();
            box-shadow: 0 4px 16px rgba(24, 144, 255, 0.4);

            &:hover {
              transform: scale(1.1);
              background: var(--primary-hover);
              box-shadow: 0 6px 20px rgba(24, 144, 255, 0.6);
            }
          }
        }

        &:hover .play-overlay {
          opacity: 1;
        }

        .play-overlay.playing {
          opacity: 0;
        }

        .vinyl-effect {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: radial-gradient(
            circle at center,
            transparent 30%,
            rgba(0, 0, 0, 0.1) 31%,
            rgba(0, 0, 0, 0.1) 32%,
            transparent 33%
          );
          pointer-events: none;

          .vinyl-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: var(--bg-primary);
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.2);

            &::after {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 20px;
              height: 20px;
              background: var(--bg-secondary);
              border-radius: 50%;
            }
          }
        }
      }

      .song-info {
        text-align: center;
        max-width: 400px;

        .song-title {
          color: var(--text-primary);
          margin: 0 0 $spacing-sm 0;
          font-size: $font-size-xxl;
          font-weight: $font-weight-bold;
          @include text-ellipsis;
        }

        .song-artist {
          color: var(--text-secondary);
          margin: 0 0 $spacing-md 0;
          font-size: $font-size-lg;
          @include text-ellipsis;
        }

        .song-tags {
          display: flex;
          justify-content: center;
          gap: $spacing-sm;
          flex-wrap: wrap;
          margin-bottom: $spacing-lg;

          .tag {
            padding: $spacing-xs $spacing-sm;
            background: rgba(255, 255, 255, 0.1);
            border-radius: $border-radius-full;
            font-size: $font-size-xs;
            color: var(--text-secondary);
            backdrop-filter: blur(4px);
          }
        }

        // 歌词容器样式
        .lyrics-container {
          margin-top: $spacing-xl;
          text-align: center;

          .lyrics-line {
            margin-bottom: $spacing-md;

            p {
              color: var(--text-primary);
              margin: 0;
              text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
              @include transition();
              line-height: 1.4;
            }

            &.first-line {
              p {
                font-size: $font-size-xl;
                font-weight: $font-weight-medium;
                color: var(--primary-color);
              }
            }

            &.second-line {
              p {
                font-size: $font-size-lg;
                opacity: 0.8;
                color: var(--text-secondary);
              }
            }
          }

          // 歌词提示
          .lyrics-hint {
            margin-top: $spacing-lg;
            text-align: center;

            p {
              color: var(--text-disabled);
              font-size: $font-size-xs;
              margin: 0;
              opacity: 0.6;
              @include transition();

              &:hover {
                opacity: 1;
                color: var(--primary-color);
              }
            }
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      color: var(--text-secondary);

      .empty-icon {
        font-size: 64px;
        color: var(--text-disabled);
        margin-bottom: $spacing-lg;
      }

      p {
        font-size: $font-size-xl;
        margin: 0 0 $spacing-lg 0;
      }
    }
  }



  // 响应式设计
  @include respond-to(xs) {
    .content-main {
      padding: $spacing-lg;

      // 全屏歌词移动端适配
      .fullscreen-lyrics {
        .fullscreen-lyrics-content {
          padding: $spacing-md;

          .fullscreen-controls-bar {
            top: 15px;
            right: 15px;
            flex-direction: row;
            align-items: center;
            gap: $spacing-sm;

            .mode-toggle {
              .mode-btn .mode-text {
                display: none; // 移动端隐藏文字
              }
            }
          }

          .fullscreen-main {
            &.mode-cover {
              .cover-lyrics-mode {
                flex-direction: column;
                gap: $spacing-lg;

                .cover-section {
                  .fullscreen-album-cover {
                    width: 280px;
                    height: 280px;

                    .fullscreen-play-overlay {
                      .fullscreen-play-button {
                        width: 60px;
                        height: 60px;
                        font-size: 24px;
                      }
                    }
                  }

                  .song-info-fullscreen {
                    .song-title {
                      font-size: $font-size-xxl;
                    }

                    .song-artist {
                      font-size: $font-size-lg;
                    }
                  }
                }

                .lyrics-section {
                  .current-lyrics {
                    .current-line {
                      font-size: 1.8rem;
                    }

                    .next-line {
                      font-size: 1.2rem;
                    }
                  }
                }
              }
            }

            &.mode-lyrics {
              .lyrics-only-mode {
                .song-header {
                  .song-title {
                    font-size: $font-size-xxl;
                  }

                  .song-artist {
                    font-size: $font-size-lg;
                  }
                }

                .lyrics-list {
                  .lyric-line {
                    font-size: $font-size-lg;
                    padding: $spacing-md 0;

                    &.active {
                      font-size: $font-size-xl;
                    }
                  }
                }
              }
            }
          }
        }
      }

      .song-display {
        .album-cover {
          width: 240px;
          height: 240px;

          .play-overlay {
            .play-button {
              width: 50px;
              height: 50px;
              font-size: 20px;
            }
          }
        }

        .song-info {
          .song-title {
            font-size: $font-size-xl;
          }

          .song-artist {
            font-size: $font-size-md;
          }

          .lyrics-container {
            margin-top: $spacing-lg;

            .lyrics-line {
              &.first-line p {
                font-size: $font-size-lg;
              }

              &.second-line p {
                font-size: $font-size-md;
              }
            }

            .lyrics-hint p {
              font-size: 10px;
            }
          }
        }
      }
    }
  }
}

// 动画定义
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>