<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import Sidebar from './components/Sidebar.vue'
import PlayerControls from './components/PlayerControls.vue'
import Breadcrumb from './components/common/Breadcrumb.vue'
import AudioPlayer from './components/AudioPlayer.vue'
import { useCategoryStore } from './stores/category'
import { usePlaylistStore } from './stores/playlist'

// Stagewise toolbar for development
import { StagewiseToolbar } from '@stagewise/toolbar-vue'

// Stagewise configuration
const stagewiseConfig = {
  plugins: []
}

// Check if we're in development mode
const isDevelopment = import.meta.env.DEV

const route = useRoute()
const currentUser = ref(null); // 当前登录用户
const categoryStore = useCategoryStore()
const playlistStore = usePlaylistStore()

// 判断是否显示播放控制栏（首页不显示）
const showPlayerControls = computed(() => {
  return route.name && route.name !== 'Discover'
})

// 判断是否显示面包屑（首页不显示）
const showBreadcrumb = computed(() => {
  return route.name && route.name !== 'Discover' && route.meta?.breadcrumb && route.meta.breadcrumb.length > 1
})

// 应用初始化
onMounted(async () => {
  console.log('应用初始化开始')

  // 初始化分类数据（首次进入网页时获取）
  try {
    await categoryStore.initializeCategories()
    console.log('分类数据初始化成功')
  } catch (error) {
    console.error('分类数据初始化失败:', error)
  }

  // 初始化歌单缓存
  try {
    playlistStore.initializeCache()
    console.log('歌单缓存初始化成功')
  } catch (error) {
    console.error('歌单缓存初始化失败:', error)
  }
})
</script>

<template>
  <div class="app-container">
    <Sidebar :isLoggedIn="!!currentUser" />
    <div class="main-content">
      <!-- 面包屑导航 -->
      <Breadcrumb v-if="showBreadcrumb" />

      <!-- 路由视图 -->
      <router-view :currentUser="currentUser" class="router-view" />

      <!-- 播放控制栏 -->
      <PlayerControls v-if="showPlayerControls" />
    </div>

    <!-- 音频播放器（全局） -->
    <AudioPlayer />

    <!-- Stagewise Toolbar - Only in development mode -->
    <StagewiseToolbar v-if="isDevelopment" :config="stagewiseConfig" />
  </div>
</template>

<style lang="scss">
@import './styles/design-system.scss';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  overflow: hidden;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-container {
  display: flex;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  position: relative;

  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-left: var(--sidebar-width);
    position: relative;
    background: linear-gradient(to bottom, var(--bg-secondary), var(--bg-tertiary));
    height: 100vh;

    .router-view {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      height: calc(100vh - var(--player-controls-height));
      // max-height: calc(100vh - var(--player-controls-height));
    }

    .player-controls {
      flex-shrink: 0;
      position: fixed;
      bottom: 0;
      right: 0;
      left: var(--sidebar-width);
      z-index: var(--z-player-controls);
      height: var(--player-controls-height);
    }
  }

  // 响应式设计
  @include respond-to(xs) {
    .main-content {
      margin-left: 0;

      > *:last-child {
        left: 0;
      }
    }
  }
}
</style>
