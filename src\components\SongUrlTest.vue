<template>
  <div class="song-url-test">
    <a-card title="音乐URL获取 API 测试" style="margin: 20px;">
      <!-- 输入区域 -->
      <div class="input-section">
        <a-space direction="vertical" style="width: 100%;" size="large">
          <!-- 歌曲ID输入 -->
          <div>
            <label>歌曲ID（支持多个，用逗号分隔）：</label>
            <a-input 
              v-model:value="songIds" 
              placeholder="例如: 347230 或 347230,347231"
              style="margin-top: 8px;"
            />
          </div>
          
          <!-- API版本选择 -->
          <div>
            <label>API版本：</label>
            <a-radio-group v-model:value="apiVersion" style="margin-top: 8px;">
              <a-radio value="v1">新版 (v1) - 推荐</a-radio>
              <a-radio value="legacy">旧版</a-radio>
            </a-radio-group>
          </div>

          <!-- 音质设置 -->
          <div v-if="apiVersion === 'v1'">
            <label>音质等级：</label>
            <a-select 
              v-model:value="qualityLevel" 
              style="width: 200px; margin-top: 8px;"
              placeholder="选择音质等级"
            >
              <a-select-option value="standard">标准</a-select-option>
              <a-select-option value="higher">较高</a-select-option>
              <a-select-option value="exhigh">极高</a-select-option>
              <a-select-option value="lossless">无损</a-select-option>
              <a-select-option value="hires">Hi-Res</a-select-option>
              <a-select-option value="jyeffect">高清环绕声</a-select-option>
              <a-select-option value="sky">沉浸环绕声</a-select-option>
              <a-select-option value="dolby">杜比全景声</a-select-option>
              <a-select-option value="jymaster">超清母带</a-select-option>
            </a-select>
          </div>

          <div v-else>
            <label>码率设置：</label>
            <a-select 
              v-model:value="bitrate" 
              style="width: 200px; margin-top: 8px;"
              placeholder="选择码率"
            >
              <a-select-option :value="128000">128k</a-select-option>
              <a-select-option :value="192000">192k</a-select-option>
              <a-select-option :value="320000">320k</a-select-option>
              <a-select-option :value="999000">最高码率</a-select-option>
            </a-select>
          </div>
          
          <!-- 操作按钮 -->
          <a-space>
            <a-button 
              type="primary" 
              @click="fetchUrls"
              :loading="urlLoading"
            >
              获取播放URL
            </a-button>
            
            <a-button 
              @click="checkAvailability"
              :loading="checkLoading"
            >
              检查可用性
            </a-button>
            
            <a-button @click="clearResults">
              清空结果
            </a-button>
          </a-space>
        </a-space>
      </div>

      <!-- 错误信息 -->
      <div v-if="error" class="error-section">
        <a-alert 
          :message="error" 
          type="error" 
          show-icon 
          style="margin-top: 16px;"
        />
      </div>

      <!-- URL结果展示 -->
      <div v-if="urlResults.length > 0" class="url-results-section">
        <a-divider>播放URL结果</a-divider>
        
        <div class="url-cards">
          <a-card 
            v-for="result in urlResults" 
            :key="result.id"
            class="url-card"
            :title="`歌曲ID: ${result.id}`"
          >
            <template #extra>
              <a-tag :color="result.available ? 'green' : 'red'">
                {{ result.available ? '可用' : '不可用' }}
              </a-tag>
            </template>

            <div class="url-info">
              <!-- 基本信息 -->
              <a-descriptions size="small" :column="2">
                <a-descriptions-item label="播放URL">
                  <div v-if="result.url" class="url-display">
                    <a-input 
                      :value="result.url" 
                      readonly 
                      style="margin-bottom: 8px;"
                    />
                    <a-space>
                      <a-button 
                        size="small" 
                        @click="copyUrl(result.url)"
                      >
                        复制URL
                      </a-button>
                      <a-button 
                        size="small" 
                        @click="testPlay(result)"
                        :disabled="!result.available"
                      >
                        试听
                      </a-button>
                    </a-space>
                  </div>
                  <span v-else class="no-url">无可用URL</span>
                </a-descriptions-item>
                
                <a-descriptions-item label="码率">
                  {{ result.br }}kbps
                </a-descriptions-item>
                
                <a-descriptions-item label="文件大小">
                  {{ formatFileSize(result.size) }}
                </a-descriptions-item>
                
                <a-descriptions-item label="编码类型">
                  {{ result.encodeType || result.type || '未知' }}
                </a-descriptions-item>
                
                <a-descriptions-item label="音质等级">
                  {{ result.level || '未知' }}
                </a-descriptions-item>
                
                <a-descriptions-item label="状态码">
                  {{ result.code }}
                </a-descriptions-item>
              </a-descriptions>

              <!-- 试听信息 -->
              <div v-if="result.freeTimeTrialPrivilege && result.freeTimeTrialPrivilege.remainTime > 0" class="trial-info">
                <a-alert 
                  message="试听版本" 
                  :description="`剩余试听时间: ${result.freeTimeTrialPrivilege.remainTime}秒`"
                  type="warning" 
                  show-icon 
                />
              </div>

              <!-- 操作区域 -->
              <div class="url-actions">
                <a-space>
                  <a-button 
                    size="small"
                    @click="viewRawUrlData(result)"
                  >
                    查看原始数据
                  </a-button>
                </a-space>
              </div>
            </div>
          </a-card>
        </div>
      </div>

      <!-- 可用性检查结果 -->
      <div v-if="availabilityResults.length > 0" class="availability-results-section">
        <a-divider>可用性检查结果</a-divider>
        
        <a-table 
          :dataSource="availabilityResults" 
          :columns="availabilityColumns"
          :pagination="false"
          size="small"
        />
      </div>

      <!-- 原始数据模态框 -->
      <a-modal
        v-model:open="rawDataVisible"
        title="原始数据"
        width="80%"
        :footer="null"
      >
        <pre class="raw-data">{{ JSON.stringify(selectedRawData, null, 2) }}</pre>
      </a-modal>

      <!-- 音频播放器 -->
      <audio 
        ref="audioPlayer" 
        controls 
        style="width: 100%; margin-top: 16px; display: none;"
        @loadstart="onAudioLoadStart"
        @canplay="onAudioCanPlay"
        @error="onAudioError"
      />
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { useMusicStore } from '../stores/music'

// Store
const musicStore = useMusicStore()

// 响应式数据
const songIds = ref('347230')
const apiVersion = ref('v1')
const qualityLevel = ref('exhigh')
const bitrate = ref(999000)
const urlLoading = ref(false)
const checkLoading = ref(false)
const error = ref('')
const urlResults = ref([])
const availabilityResults = ref([])
const rawDataVisible = ref(false)
const selectedRawData = ref(null)
const audioPlayer = ref(null)

// 可用性检查表格列配置
const availabilityColumns = [
  { title: '歌曲ID', dataIndex: 'id', key: 'id' },
  { 
    title: '可用性', 
    dataIndex: 'available', 
    key: 'available',
    customRender: ({ text }) => text ? '✅ 可用' : '❌ 不可用'
  },
  { title: '消息', dataIndex: 'message', key: 'message' },
  { title: '码率', dataIndex: 'br', key: 'br', customRender: ({ text }) => `${text}kbps` }
]

// 方法
async function fetchUrls() {
  if (!songIds.value.trim()) {
    message.warning('请输入歌曲ID')
    return
  }

  urlLoading.value = true
  error.value = ''
  urlResults.value = []

  try {
    // 解析ID
    const ids = songIds.value.split(',').map(id => id.trim()).filter(id => id)
    
    if (ids.length === 0) {
      throw new Error('请输入有效的歌曲ID')
    }

    let result
    if (apiVersion.value === 'v1') {
      // 使用新版API
      result = await musicStore.fetchSongUrlV1(ids.length === 1 ? ids[0] : ids, qualityLevel.value)
    } else {
      // 使用旧版API
      result = await musicStore.fetchSongUrl(ids.length === 1 ? ids[0] : ids, bitrate.value)
    }
    
    // 处理结果
    urlResults.value = Array.isArray(result) ? result : [result]
    
    message.success(`成功获取 ${urlResults.value.length} 个音乐URL`)
  } catch (err) {
    error.value = err.message || '获取音乐URL失败'
    console.error('获取音乐URL失败:', err)
  } finally {
    urlLoading.value = false
  }
}

async function checkAvailability() {
  if (!songIds.value.trim()) {
    message.warning('请输入歌曲ID')
    return
  }

  checkLoading.value = true
  availabilityResults.value = []

  try {
    // 解析ID
    const ids = songIds.value.split(',').map(id => id.trim()).filter(id => id)
    
    // 逐个检查可用性
    const results = []
    for (const id of ids) {
      const result = await musicStore.checkMusicAvailable(id, bitrate.value)
      results.push(result)
    }
    
    availabilityResults.value = results
    message.success(`完成 ${results.length} 首歌曲可用性检查`)
  } catch (err) {
    error.value = err.message || '检查音乐可用性失败'
    console.error('检查音乐可用性失败:', err)
  } finally {
    checkLoading.value = false
  }
}

function clearResults() {
  urlResults.value = []
  availabilityResults.value = []
  error.value = ''
}

function copyUrl(url) {
  navigator.clipboard.writeText(url).then(() => {
    message.success('URL已复制到剪贴板')
  }).catch(() => {
    message.error('复制失败')
  })
}

function testPlay(result) {
  if (!result.url) {
    message.warning('无可用播放URL')
    return
  }

  const audio = audioPlayer.value
  audio.src = result.url
  audio.style.display = 'block'
  audio.load()
  
  message.info('正在加载音频...')
}

function viewRawUrlData(result) {
  selectedRawData.value = result.raw
  rawDataVisible.value = true
}

function formatFileSize(bytes) {
  if (!bytes) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 音频事件处理
function onAudioLoadStart() {
  message.info('开始加载音频')
}

function onAudioCanPlay() {
  message.success('音频加载完成，可以播放')
}

function onAudioError(event) {
  console.error('音频播放错误:', event)
  message.error('音频播放失败，可能是URL无效或网络问题')
}
</script>

<style lang="scss" scoped>
.song-url-test {
  .input-section {
    margin-bottom: 20px;
    
    label {
      font-weight: 500;
      color: #333;
      display: block;
    }
  }

  .url-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
    gap: 16px;
    margin-top: 16px;
  }

  .url-card {
    .url-info {
      .url-display {
        .ant-input {
          font-family: monospace;
          font-size: 12px;
        }
      }

      .no-url {
        color: #999;
        font-style: italic;
      }

      .trial-info {
        margin: 16px 0;
      }

      .url-actions {
        padding-top: 12px;
        border-top: 1px solid #f0f0f0;
      }
    }
  }

  .raw-data {
    background: #f5f5f5;
    padding: 16px;
    border-radius: 4px;
    font-size: 12px;
    max-height: 500px;
    overflow-y: auto;
  }
}
</style>
