import axios from 'axios';

// 创建axios实例
const service = axios.create({
  baseURL: 'http://150.158.43.187:3000',
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 可以在这里添加token等认证信息
    const token = localStorage.getItem('music_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    const { data } = response;
    
    // 检查响应状态码
    if (data.code === 200) {
      return data;
    } else {
      console.error('API错误:', data.message || '请求失败');
      return Promise.reject(new Error(data.message || '请求失败'));
    }
  },
  error => {
    console.error('响应拦截器错误:', error);
    
    // 处理网络错误
    if (error.code === 'ECONNABORTED') {
      console.error('请求超时');
    } else if (error.response) {
      console.error('服务器错误:', error.response.status);
    } else {
      console.error('网络错误');
    }
    
    return Promise.reject(error);
  }
);

export default service;
