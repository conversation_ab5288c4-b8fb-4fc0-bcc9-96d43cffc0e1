import { defineStore } from 'pinia';
import request from '@/utils/request';

export const usePlaylistDetailStore = defineStore('playlistDetail', {
  state: () => ({
    // 歌单详情数据
    playlist: null,
    // 歌单所有歌曲
    tracks: [],
    // 加载状态
    loading: false,
    tracksLoading: false,
    // 错误信息
    error: null,
    tracksError: null,
    // 当前歌单ID
    currentPlaylistId: null,
    // 分页信息
    pagination: {
      offset: 0,
      limit: 50,
      total: 0,
      hasMore: true
    }
  }),

  getters: {
    // 格式化的歌单信息
    formattedPlaylist: (state) => {
      if (!state.playlist) return null;
      
      return {
        id: state.playlist.id,
        name: state.playlist.name,
        description: state.playlist.description,
        coverUrl: state.playlist.coverImgUrl,
        playCount: state.playlist.playCount,
        subscribedCount: state.playlist.subscribedCount,
        shareCount: state.playlist.shareCount,
        commentCount: state.playlist.commentCount,
        trackCount: state.playlist.trackCount,
        tags: state.playlist.tags || [],
        creator: {
          id: state.playlist.creator?.userId,
          nickname: state.playlist.creator?.nickname,
          avatarUrl: state.playlist.creator?.avatarUrl,
          signature: state.playlist.creator?.signature
        },
        createTime: state.playlist.createTime,
        updateTime: state.playlist.updateTime,
        subscribed: state.playlist.subscribed
      };
    },

    // 格式化的歌曲列表
    formattedTracks: (state) => {
      return state.tracks.map((track, index) => ({
        id: track.id,
        name: track.name,
        title: track.name, // 兼容其他组件
        artist: track.ar?.map(artist => artist.name).join(' / ') || '未知艺术家',
        artists: track.ar || [],
        album: track.al?.name || '未知专辑',
        albumId: track.al?.id,
        coverUrl: track.al?.picUrl,
        duration: track.dt, // 毫秒
        durationFormatted: formatDuration(track.dt),
        trackNumber: index + 1,
        fee: track.fee, // 收费类型
        mvId: track.mv,
        popularity: track.pop,
        alias: track.alia || [],
        publishTime: track.publishTime,
        // 音质信息
        quality: {
          standard: track.l,
          higher: track.m, 
          exhigh: track.h,
          lossless: track.sq,
          hires: track.hr
        }
      }));
    },

    // 是否有更多数据
    hasMoreTracks: (state) => {
      return state.pagination.hasMore && state.tracks.length < state.pagination.total;
    }
  },

  actions: {
    // 获取歌单详情
    async fetchPlaylistDetail(playlistId) {
      if (this.currentPlaylistId === playlistId && this.playlist) {
        return this.formattedPlaylist;
      }

      this.loading = true;
      this.error = null;

      try {
        console.log('获取歌单详情:', playlistId);
        const response = await request.get('/playlist/detail', {
          params: { id: playlistId }
        });

        if (response && response.playlist) {
          this.playlist = response.playlist;
          this.currentPlaylistId = playlistId;
          
          // 更新分页信息
          this.pagination.total = response.playlist.trackCount;
          
          console.log('歌单详情获取成功:', this.formattedPlaylist);
          return this.formattedPlaylist;
        } else {
          throw new Error('歌单数据格式错误');
        }
      } catch (err) {
        console.error('获取歌单详情失败:', err);
        this.error = err.message || '获取歌单详情失败';
        throw err;
      } finally {
        this.loading = false;
      }
    },

    // 获取歌单所有歌曲
    async fetchPlaylistTracks(playlistId, options = {}) {
      const { 
        offset = 0, 
        limit = 50, 
        append = false 
      } = options;

      this.tracksLoading = true;
      this.tracksError = null;

      try {
        console.log('获取歌单歌曲:', { playlistId, offset, limit });
        const response = await request.get('/playlist/track/all', {
          params: { 
            id: playlistId,
            offset,
            limit
          }
        });

        console.log('歌单歌曲API响应:', response);

        if (response && (response.songs || response.tracks || response.data)) {
          let newTracks = response.songs || response.tracks || response.data;

          // 如果歌曲数据缺少艺术家信息，使用 song/detail API 获取完整信息
          if (newTracks.length > 0 && (!newTracks[0].ar || newTracks[0].ar.length === 0)) {
            console.log('歌曲数据缺少艺术家信息，使用 song/detail API 获取完整信息');
            try {
              const songIds = newTracks.map(track => track.id).join(',');
              const detailResponse = await request.get('/song/detail', {
                params: { ids: songIds }
              });

              if (detailResponse && detailResponse.songs) {
                // 创建一个ID到详细信息的映射
                const detailMap = new Map();
                detailResponse.songs.forEach(song => {
                  detailMap.set(song.id, song);
                });

                // 保持原始顺序，只替换有详细信息的歌曲
                newTracks = newTracks.map(track => {
                  const detailTrack = detailMap.get(track.id);
                  return detailTrack || track; // 如果有详细信息就用详细信息，否则用原始数据
                });

                console.log('通过 song/detail API 获取到完整歌曲信息');
              }
            } catch (detailError) {
              console.warn('获取歌曲详细信息失败，使用原始数据:', detailError);
            }
          }

          if (append) {
            console.log('追加模式：当前歌曲数量:', this.tracks.length, '新增歌曲数量:', newTracks.length);
            this.tracks = [...this.tracks, ...newTracks];
            console.log('追加后总数量:', this.tracks.length);
          } else {
            console.log('替换模式：新歌曲数量:', newTracks.length);
            this.tracks = newTracks;
          }

          // 更新分页信息
          this.pagination.offset = offset;
          this.pagination.limit = limit;
          this.pagination.hasMore = newTracks.length === limit;

          console.log('歌单歌曲获取成功，数量:', newTracks.length, '总数量:', this.tracks.length);
          return this.formattedTracks;
        } else {
          throw new Error('歌曲数据格式错误');
        }
      } catch (err) {
        console.error('获取歌单歌曲失败:', err);
        this.tracksError = err.message || '获取歌单歌曲失败';
        throw err;
      } finally {
        this.tracksLoading = false;
      }
    },

    // 加载更多歌曲
    async loadMoreTracks() {
      if (!this.hasMoreTracks || this.tracksLoading) {
        return;
      }

      const nextOffset = this.pagination.offset + this.pagination.limit;
      return await this.fetchPlaylistTracks(this.currentPlaylistId, {
        offset: nextOffset,
        limit: this.pagination.limit,
        append: true
      });
    },

    // 初始化歌单详情页数据
    async initializePlaylistDetail(playlistId) {
      try {
        // 并行获取歌单详情和歌曲列表
        const [playlistDetail] = await Promise.all([
          this.fetchPlaylistDetail(playlistId),
          this.fetchPlaylistTracks(playlistId, { limit: 50 })
        ]);

        return playlistDetail;
      } catch (err) {
        console.error('初始化歌单详情失败:', err);
        throw err;
      }
    },

    // 清空数据
    clearPlaylistDetail() {
      this.playlist = null;
      this.tracks = [];
      this.currentPlaylistId = null;
      this.error = null;
      this.tracksError = null;
      this.pagination = {
        offset: 0,
        limit: 50,
        total: 0,
        hasMore: true
      };
    }
  }
});

// 格式化时长的辅助函数
function formatDuration(milliseconds) {
  if (!milliseconds) return '00:00';
  
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}
