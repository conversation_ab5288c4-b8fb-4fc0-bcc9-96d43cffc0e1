import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import request from '../utils/request';

export const useCategoryStore = defineStore('category', () => {
  // 状态
  const categories = ref([]);
  const loading = ref(false);
  const error = ref(null);
  const initialized = ref(false);

  // 默认分类（包含"全部"选项）
  const defaultCategories = [
    { id: 0, name: '全部', type: 0, category: 0, hot: false }
  ];

  // 获取分类数据
  async function fetchCategories() {
    if (initialized.value) {
      return categories.value;
    }

    loading.value = true;
    error.value = null;

    try {
      console.log('开始请求分类数据...');
      const response = await request.get('/playlist/highquality/tags');
      console.log('API响应:', response);

      if (response && response.tags) {
        // 合并默认分类和API返回的分类
        categories.value = [...defaultCategories, ...response.tags];
        initialized.value = true;
        
        // 持久化到localStorage
        localStorage.setItem('music_categories', JSON.stringify(categories.value));
        localStorage.setItem('music_categories_timestamp', Date.now().toString());

        console.log('分类数据获取成功，共', categories.value.length, '个分类:', categories.value);
      } else {
        throw new Error('分类数据格式错误');
      }
    } catch (err) {
      console.error('获取分类数据失败:', err);
      error.value = err.message || '获取分类数据失败';
      
      // 如果API失败，尝试从localStorage加载
      loadFromLocalStorage();
    } finally {
      loading.value = false;
    }

    return categories.value;
  }

  // 从localStorage加载分类数据
  function loadFromLocalStorage() {
    try {
      const savedCategories = localStorage.getItem('music_categories');
      const timestamp = localStorage.getItem('music_categories_timestamp');

      console.log('检查缓存数据:', { savedCategories: !!savedCategories, timestamp });

      if (savedCategories && timestamp) {
        const now = Date.now();
        const savedTime = parseInt(timestamp);
        const oneDay = 24 * 60 * 60 * 1000; // 24小时
        const timeDiff = now - savedTime;

        console.log('缓存时间检查:', { now, savedTime, timeDiff, oneDay, isValid: timeDiff < oneDay });

        // 如果数据不超过24小时，使用缓存数据
        if (timeDiff < oneDay) {
          categories.value = JSON.parse(savedCategories);
          initialized.value = true;
          console.log('从缓存加载分类数据，数量:', categories.value.length);
          return true;
        } else {
          console.log('缓存数据已过期');
        }
      } else {
        console.log('没有找到缓存数据');
      }
    } catch (err) {
      console.error('从缓存加载分类数据失败:', err);
    }

    // 如果缓存失败，使用默认分类
    console.log('使用默认分类');
    categories.value = defaultCategories;
    // 注意：这里不设置initialized.value = true，让API请求可以继续
    return false;
  }

  // 初始化分类数据（首次进入网页时调用）
  async function initializeCategories() {
    if (initialized.value) {
      console.log('分类数据已初始化，跳过');
      return categories.value;
    }

    console.log('开始初始化分类数据...');

    // 先尝试从缓存加载
    const loadedFromCache = loadFromLocalStorage();
    console.log('缓存加载结果:', loadedFromCache);

    // 如果缓存加载成功，直接返回；否则从API获取
    if (!loadedFromCache) {
      console.log('缓存无效，从API获取数据');
      await fetchCategories();
    } else {
      console.log('使用缓存数据，分类数量:', categories.value.length);
    }

    return categories.value;
  }

  // 格式化分类数据为组件需要的格式
  const formattedCategories = computed(() => {
    return categories.value.map(category => ({
      value: category.id === 0 ? 'all' : category.name,
      label: getCategoryLabel(category)
    }));
  });

  // 获取分类标签（添加emoji）
  function getCategoryLabel(category) {
    const emojiMap = {
      '全部': '🎵',
      '华语': '🇨🇳',
      '流行': '🎤',
      '摇滚': '🎸',
      '民谣': '🎻',
      '电子': '🎧',
      '说唱': '🎤',
      '轻音乐': '🎼',
      '爵士': '🎺',
      '乡村': '🤠',
      '蓝调': '🎷',
      '古典': '🎹',
      '新世纪': '✨',
      '世界音乐': '🌍'
    };

    const emoji = emojiMap[category.name] || '🎵';
    return `${emoji} ${category.name}`;
  }

  // 清除缓存
  function clearCache() {
    localStorage.removeItem('music_categories');
    localStorage.removeItem('music_categories_timestamp');
    categories.value = [];
    initialized.value = false;
  }

  return {
    categories,
    loading,
    error,
    initialized,
    formattedCategories,
    fetchCategories,
    initializeCategories,
    loadFromLocalStorage,
    clearCache
  };
});
