# 播放系统使用指南

## 系统架构

### 核心组件
```mermaid
graph TB
    A[useMusicStore] --> B[AudioPlayer]
    A --> C[PlayerControls]
    A --> D[Sidebar播放列表]
    A --> E[PlaylistDetail歌单页]
    
    B --> F[HTML5 Audio]
    C --> G[进度条控制]
    D --> H[播放列表管理]
    E --> I[双击播放]
```

### 数据流
1. **歌单双击** → 获取歌曲详情 → 获取播放URL → 设置播放列表 → 开始播放
2. **播放控制** → 音频元素控制 → 状态同步 → UI更新
3. **列表管理** → Store状态 → 组件响应 → 界面更新

## 功能特性

### ✅ 已实现功能

#### 1. 完整播放流程
- **歌曲详情获取**：通过API获取歌曲信息
- **播放URL获取**：支持多种音质（标准/较高/极高/无损/Hi-Res）
- **权限检查**：自动检查播放权限和可用性
- **音频播放**：HTML5 Audio元素实现

#### 2. 播放列表管理
- **动态播放列表**：支持单曲和歌单播放
- **列表操作**：添加、删除、清空播放列表
- **索引管理**：自动维护当前播放位置
- **历史记录**：播放历史持久化存储

#### 3. 播放控制
- **基础控制**：播放/暂停/上一首/下一首
- **进度控制**：拖拽进度条跳转播放位置
- **音量控制**：音量调节和静音
- **播放模式**：顺序/随机/单曲循环（已预留）

#### 4. 状态同步
- **实时状态**：播放状态、进度、时长同步
- **全局事件**：组件间通过事件通信
- **持久化**：播放历史和设置本地存储

## 使用方法

### 1. 歌单双击播放

在歌单详情页面，双击任意歌曲即可开始播放：

```javascript
// PlaylistDetail.vue 中的实现
async function playSong(song) {
  try {
    // 获取当前歌单的所有歌曲作为播放列表
    const formattedTracks = tracks.value.map(track => ({
      id: track.id,
      title: track.name,
      artist: track.ar?.map(artist => artist.name).join(' / ') || '未知歌手',
      album: track.al?.name || '未知专辑',
      coverUrl: track.al?.picUrl || '',
      duration: track.dt || 0,
      durationText: musicStore.formatDuration(track.dt),
      raw: track
    }))
    
    // 播放指定歌曲，并设置播放列表
    await musicStore.playSong(song.id, formattedTracks)
    message.success(`开始播放：${song.name}`)
  } catch (error) {
    console.error('播放歌曲失败:', error)
    message.error(error.message || '播放失败')
  }
}
```

### 2. 编程式播放控制

```javascript
import { useMusicStore } from '@/stores/music'

const musicStore = useMusicStore()

// 播放单首歌曲
await musicStore.playSong('347230')

// 播放歌单
await musicStore.playPlaylist(songs, 0)

// 播放控制
musicStore.togglePlay()
await musicStore.nextSong()
await musicStore.prevSong()

// 设置音量
musicStore.setVolume(80)

// 播放列表管理
musicStore.clearPlaylist()
musicStore.removeSongFromPlaylist(index)
```

### 3. 状态监听

```javascript
import { watch } from 'vue'

// 监听当前歌曲变化
watch(() => musicStore.currentSong, (newSong) => {
  console.log('当前播放:', newSong?.title)
})

// 监听播放状态
watch(() => musicStore.isPlaying, (isPlaying) => {
  console.log('播放状态:', isPlaying ? '播放中' : '暂停')
})

// 监听播放列表变化
watch(() => musicStore.playlist, (playlist) => {
  console.log('播放列表长度:', playlist.length)
})
```

## API 接口

### Store 状态

```javascript
{
  // 播放状态
  currentSong: null,          // 当前播放歌曲
  isPlaying: false,           // 是否正在播放
  playlist: [],               // 播放列表
  currentIndex: 0,            // 当前播放索引
  volume: 100,                // 音量 (0-100)
  loading: false,             // 加载状态
  error: '',                  // 错误信息
  
  // 播放设置
  playMode: 'order',          // 播放模式
  playQuality: 'exhigh',      // 播放质量
  playHistory: [],            // 播放历史
}
```

### Store 方法

```javascript
// 播放控制
playSong(songOrId, playlistSongs)     // 播放歌曲
playPlaylist(songs, startIndex)      // 播放歌单
togglePlay()                         // 切换播放/暂停
nextSong()                          // 下一首
prevSong()                          // 上一首

// 列表管理
clearPlaylist()                     // 清空播放列表
removeSongFromPlaylist(index)       // 移除歌曲

// 设置方法
setVolume(volume)                   // 设置音量
setPlayMode(mode)                   // 设置播放模式
setPlayQuality(quality)             // 设置播放质量

// 工具方法
toggleLike(songId)                  // 切换喜欢
isLiked(songId)                     // 检查是否喜欢
addToPlayHistory(song)              // 添加到历史
```

## 测试页面

### 访问测试页面
- **播放系统测试**：`http://localhost:5173/test/play-system`
- **歌曲详情测试**：`http://localhost:5173/test/song-detail`
- **音乐URL测试**：`http://localhost:5173/test/song-url`

### 测试功能
1. **单曲播放测试**：测试播放指定歌曲
2. **播放列表测试**：测试播放整个歌单
3. **播放控制测试**：测试播放/暂停/切歌
4. **音量控制测试**：测试音量调节
5. **状态显示**：实时显示播放状态和歌曲信息

## 错误处理

### 常见错误及解决方案

1. **播放权限错误**
   ```
   错误：该歌曲暂无播放权限
   解决：检查歌曲的 privilege.canPlay 字段
   ```

2. **播放链接错误**
   ```
   错误：该歌曲暂无可用播放链接
   解决：检查网络连接，或尝试其他音质
   ```

3. **网络错误**
   ```
   错误：网络错误
   解决：检查网络连接和API服务状态
   ```

4. **音频格式错误**
   ```
   错误：音频格式不支持
   解决：浏览器不支持该音频格式
   ```

## 最佳实践

### 1. 性能优化
- 使用批量API获取歌曲信息
- 预加载下一首歌曲的播放链接
- 合理设置音频预加载策略

### 2. 用户体验
- 显示加载状态和进度
- 提供友好的错误提示
- 支持键盘快捷键控制

### 3. 错误恢复
- 播放失败时自动跳过
- 网络错误时重试机制
- 降级音质策略

## 扩展功能

### 计划中的功能
- [ ] 播放模式切换（随机/循环）
- [ ] 歌词显示和同步
- [ ] 音效均衡器
- [ ] 播放队列管理
- [ ] 跨设备同步
- [ ] 离线播放支持

### 自定义扩展
可以通过监听 Store 状态变化来实现自定义功能：

```javascript
// 自定义播放统计
watch(() => musicStore.currentSong, (song) => {
  if (song) {
    // 记录播放统计
    recordPlayStats(song.id)
  }
})

// 自定义播放完成处理
window.addEventListener('audio-ended', () => {
  // 自定义播放完成逻辑
  handlePlayComplete()
})
```

## 技术细节

### 音频处理
- 使用 HTML5 Audio API
- 支持多种音频格式（MP3、AAC等）
- 自动处理音频加载和缓冲

### 状态管理
- 基于 Pinia 的响应式状态管理
- 组件间通过 Store 共享状态
- 支持状态持久化

### 事件通信
- 全局事件用于组件间通信
- 音频事件自动同步到 Store
- 支持自定义事件监听

这个播放系统提供了完整的音乐播放功能，支持从歌单双击播放到完整的播放控制，是一个功能完备的音乐播放解决方案。
