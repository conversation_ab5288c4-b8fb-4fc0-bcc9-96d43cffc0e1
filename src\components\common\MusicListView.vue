<template>
  <div class="music-list-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="playlist-info">
        <div class="playlist-cover" @click="playAll">
          <img :src="coverImage" :alt="title" />
          <div class="play-overlay">
            <play-circle-filled class="play-icon" />
          </div>
        </div>
        <div class="playlist-details">
          <h1 class="playlist-title">{{ title }}</h1>
          <div class="playlist-meta">
            <a-avatar size="small" class="creator-avatar">
              <template #icon><user-outlined /></template>
            </a-avatar>
            <span class="creator-name">{{ currentUser?.username || 'Guest' }}</span>
            <span class="create-time">{{ createTime }}</span>
            <span class="separator">•</span>
            <span class="song-count">{{ filteredSongs.length }}首歌</span>
            <span class="separator">•</span>
            <span class="play-count">播放 {{ playCount }}</span>
            <span class="separator">•</span>
            <span class="collect-count">收藏 {{ collectCount }}</span>
          </div>
          <div class="playlist-actions">
            <a-button type="primary" class="play-all-btn" @click="playAll">
              <play-circle-outlined />
              播放全部
            </a-button>
            <a-button class="action-btn">
              <download-outlined />
              下载
            </a-button>
            <a-button class="action-btn">
              <more-outlined />
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 歌曲列表 -->
    <div class="song-list-container">
      <div class="list-header">
        <div class="header-tabs">
          <span class="tab active">歌曲</span>
        </div>
        <div class="search-box">
          <a-input 
            placeholder="搜索歌曲、歌手、专辑" 
            size="small" 
            class="search-input"
            v-model:value="searchQuery"
            @input="handleSearch"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
        </div>
      </div>

      <div class="song-list">

        <div class="song-table">
          <div class="table-header">
            <div class="col-index"></div>
            <div class="col-title">音乐标题</div>
            <div class="col-artist">歌手</div>
            <div class="col-album">专辑</div>
            <div class="col-duration">时长</div>
          </div>

          <div class="table-body">
            <div v-for="(song, index) in filteredSongs" :key="song.id" class="song-row"
              :class="{ active: currentPlayingSong?.id === song.id, playing: isPlaying && currentPlayingSong?.id === song.id }"
              @dblclick="playSong(song)">
              <div class="col-index">
                <span class="song-number" v-if="currentPlayingSong?.id !== song.id">{{ String(index + 1).padStart(2, '0') }}</span>
                <play-circle-outlined v-else-if="!isPlaying" class="play-btn" @click="resumePlay" />
                <pause-circle-outlined v-else class="pause-btn" @click="pausePlay" />
              </div>
              <div class="col-title">
                <div class="song-info">
                  <span class="song-name">{{ song.title }}</span>
                  <div class="song-tags" v-if="song.tags?.length">
                    <span v-for="tag in song.tags" :key="tag" class="tag">{{ tag }}</span>
                  </div>
                </div>
              </div>
              <div class="col-artist">
                <span class="artist-name">{{ song.artist }}</span>
              </div>
              <div class="col-album">
                <span class="album-name">{{ song.album }}</span>
              </div>
              <div class="col-duration">
                <div class="duration-actions">
                  <heart-filled v-if="song.liked" class="like-btn liked" @click="toggleLike(song)" />
                  <heart-outlined v-else class="like-btn" @click="toggleLike(song)" />
                  <download-outlined class="action-icon" />
                  <share-alt-outlined class="action-icon" />
                  <more-outlined class="action-icon" />
                  <span class="duration">{{ song.duration }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useMusicStore } from '../../stores/music';
import {
  PlayCircleFilled,
  PlayCircleOutlined,
  PauseCircleOutlined,
  UserOutlined,
  DownloadOutlined,
  MoreOutlined,
  SearchOutlined,
  HeartFilled,
  HeartOutlined,
  ShareAltOutlined
} from '@ant-design/icons-vue';

const musicStore = useMusicStore();

// Props
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  coverImage: {
    type: String,
    default: () => new URL('../../assets/preview.jpg', import.meta.url).href
  },
  songs: {
    type: Array,
    default: () => []
  },
  currentUser: {
    type: Object,
    default: null
  },
  createTime: {
    type: String,
    default: '2025-6-4创建'
  },
  playCount: {
    type: String,
    default: '10w+'
  },
  collectCount: {
    type: String,
    default: '2858'
  }
});

// Emits
const emit = defineEmits(['play-song', 'toggle-like']);

// 响应式数据
const isPlaying = ref(false);
const currentPlayingSong = ref(null);
const searchQuery = ref('');
const originalSongs = ref([]);

// 初始化
onMounted(() => {
  originalSongs.value = [...props.songs];
});

// 监听 props.songs 变化
watch(() => props.songs, (newSongs) => {
  originalSongs.value = [...newSongs];
}, { immediate: true });

// 过滤后的歌曲列表
const filteredSongs = computed(() => {
  if (!searchQuery.value) {
    return originalSongs.value;
  }
  
  const keyword = searchQuery.value.toLowerCase();
  return originalSongs.value.filter(song => {
    return song.title.toLowerCase().includes(keyword) || 
           song.artist.toLowerCase().includes(keyword) || 
           song.album.toLowerCase().includes(keyword);
  });
});

// 方法
function handleSearch(e) {
  searchQuery.value = e.target.value;
}

function playAll() {
  if (filteredSongs.value.length > 0) {
    playSong(filteredSongs.value[0]);
  }
}

function playSong(song) {
  currentPlayingSong.value = song;
  isPlaying.value = true;
  musicStore.currentSong = song;
  emit('play-song', song);
}

function pausePlay() {
  isPlaying.value = false;
  musicStore.togglePlay?.();
}

function resumePlay() {
  isPlaying.value = true;
  musicStore.togglePlay?.();
}

function toggleLike(song) {
  song.liked = !song.liked;
  musicStore.toggleLike?.(song.id);
  emit('toggle-like', song);
}
</script>

<style lang="scss" scoped>
@import '../../styles/design-system.scss';

.music-list-view {
  height: 100%;
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  color: var(--text-primary);
  overflow-y: scroll; // 强制显示滚动条，避免内容变化时的抖动
  overflow-x: hidden;
  @include custom-scrollbar(); // 使用自定义滚动条样式
  padding-bottom: var(--player-controls-height);

  .page-header {
    padding: $spacing-xl;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(10px);

    .playlist-info {
      display: flex;
      gap: $spacing-lg;

      .playlist-cover {
        position: relative;
        width: 200px;
        height: 200px;
        border-radius: $border-radius-lg;
        overflow: hidden;
        cursor: pointer;
        @include hover-lift;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .play-overlay {
          position: absolute;
          inset: 0;
          background: var(--bg-overlay);
          @include flex-center;
          opacity: 0;
          @include transition(opacity);

          .play-icon {
            font-size: 60px;
            color: var(--text-primary);
            cursor: pointer;
          }
        }

        &:hover .play-overlay {
          opacity: 1;
        }
      }

      .playlist-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .playlist-title {
          font-size: $font-size-xxxl;
          font-weight: $font-weight-bold;
          margin-bottom: $spacing-md;
        }

        .playlist-meta {
          display: flex;
          align-items: center;
          gap: $spacing-sm;
          margin-bottom: $spacing-lg;
          font-size: $font-size-md;
          flex-wrap: wrap;

          .creator-avatar {
            background-color: var(--bg-card);
          }

          .creator-name {
            color: var(--primary-color);
            cursor: pointer;
            font-weight: $font-weight-medium;
            @include transition(color);

            &:hover {
              color: var(--primary-hover);
            }
          }

          .create-time {
            color: var(--text-secondary);
            opacity: 0.8;
          }

          .separator {
            color: var(--text-secondary);
            opacity: 0.5;
            margin: 0 6px;
          }

          .song-count {
            color: var(--text-primary);
            font-weight: $font-weight-medium;
            opacity: 0.9;
          }

          .play-count,
          .collect-count {
            color: var(--primary-color);
            font-weight: $font-weight-medium;
            opacity: 1;
          }
        }

        .playlist-actions {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: $spacing-md;

          .play-all-btn {
            background: #ec4141;
            border: none;
            border-radius: $spacing-lg;
            padding: 12px 32px;
            font-size: $font-size-lg;
            font-weight: $font-weight-medium;
            height: 48px;
            min-width: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: $spacing-sm;
            @include transition();

            &:hover {
              background: #d73027;
              transform: translateY(-1px);
            }
          }

          .action-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            border-radius: $spacing-lg;
            padding: 12px 20px;
            font-size: $font-size-md;
            height: 48px;
            min-width: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            @include transition();

            &:hover {
              background: rgba(255, 255, 255, 0.2);
              border-color: var(--border-hover);
              transform: translateY(-1px);
            }
          }
        }
      }
    }
  }

  .song-list-container {
    padding: 0 $spacing-xl $spacing-xl;

    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--border-color);
      padding: $spacing-md;
      position: relative;

      .header-tabs {
        display: flex;
        gap: $spacing-xxl;

        .tab {
          font-size: $font-size-lg;
          cursor: pointer;
          padding-bottom: $spacing-sm;
          opacity: 0.6;
          @include transition(opacity);

          &.active {
            opacity: 1;
            border-bottom: 2px solid #ec4141;
          }

          &:hover {
            opacity: 0.8;
          }
        }
      }

      .search-box {
        .search-input {
          width: 240px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid var(--border-color);
          border-radius: $spacing-md;

          :deep(.ant-input) {
            background: transparent;
            color: var(--text-primary);
            border: none;
            padding: 8px 12px;

            &::placeholder {
              color: var(--text-secondary);
            }
          }

          :deep(.ant-input-prefix) {
            color: var(--text-secondary);
            margin-right: 8px;
          }
        }
      }
    }

    .song-list {

      .song-table {
        .table-header {
          display: flex;
          padding: 12px 0;
          border-bottom: 1px solid var(--border-color);
          font-size: $font-size-md;
          opacity: 0.6;
          font-weight: $font-weight-medium;
          margin-bottom: 8px;

          .col-index {
            width: 60px;
            text-align: center;
            padding: 0 8px;
          }

          .col-title {
            flex: 2.5;
            padding-left: $spacing-md;
            min-width: 200px;
          }

          .col-artist {
            flex: 1.5;
            padding: 0 8px;
            min-width: 120px;
          }

          .col-album {
            flex: 1.5;
            padding: 0 8px;
            min-width: 120px;
          }

          .col-duration {
            width: 180px;
            text-align: right;
            padding-right: $spacing-md;
          }
        }

        .table-body {
          padding-bottom: $spacing-xl;

          .song-row {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-radius: $border-radius-sm;
            @include transition(background-color);
            cursor: pointer;
            border-bottom: 1px solid transparent;

            &:hover {
              background: rgba(255, 255, 255, 0.05);
              border-bottom-color: rgba(255, 255, 255, 0.1);
            }

            &.active {
              background: rgba(236, 65, 65, 0.1);
              color: #ec4141;
            }

            &.playing {
              .song-name {
                color: #ec4141;
              }
            }

            .col-index {
              width: 60px;
              text-align: center;
              font-size: $font-size-md;
              opacity: 0.6;
              padding: 0 8px;

              .play-btn,
              .pause-btn {
                font-size: $font-size-lg;
                color: #ec4141;
                cursor: pointer;
              }
            }

            .col-title {
              flex: 2.5;
              padding-left: $spacing-md;
              min-width: 200px;

              .song-info {
                .song-name {
                  font-size: $font-size-md;
                  font-weight: $font-weight-medium;
                  margin-bottom: 4px;
                  @include text-ellipsis;
                }

                .song-tags {
                  display: flex;
                  gap: 4px;
                  flex-wrap: wrap;

                  .tag {
                    background: rgba(236, 65, 65, 0.2);
                    color: #ec4141;
                    padding: 2px 6px;
                    border-radius: 2px;
                    font-size: $font-size-xs;
                  }
                }
              }
            }

            .col-artist {
              flex: 1.5;
              font-size: $font-size-md;
              opacity: 0.8;
              padding: 0 8px;
              min-width: 120px;

              .artist-name {
                cursor: pointer;
                @include transition(color);
                @include text-ellipsis;

                &:hover {
                  color: var(--primary-color);
                }
              }
            }

            .col-album {
              flex: 1.5;
              font-size: $font-size-md;
              opacity: 0.8;
              padding: 0 8px;
              min-width: 120px;

              .album-name {
                cursor: pointer;
                @include transition(color);
                @include text-ellipsis;

                &:hover {
                  color: var(--primary-color);
                }
              }
            }

            .col-duration {
              width: 180px;
              text-align: right;
              padding-right: $spacing-md;

              .duration-actions {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                gap: 10px;
                opacity: 0;
                @include transition(opacity);

                .like-btn {
                  font-size: $font-size-lg;
                  cursor: pointer;
                  color: var(--text-secondary);
                  @include transition(color);

                  &.liked {
                    color: #ec4141;
                    opacity: 1;
                  }

                  &:hover {
                    color: #ec4141;
                  }
                }

                .action-icon {
                  font-size: $font-size-sm;
                  cursor: pointer;
                  color: var(--text-secondary);
                  @include transition(color);

                  &:hover {
                    color: var(--text-primary);
                  }
                }

                .duration {
                  font-size: $font-size-sm;
                  opacity: 0.6;
                  min-width: 45px;
                  margin-left: 8px;
                }
              }
            }

            &:hover .duration-actions {
              opacity: 1;
            }

            // 已喜欢的歌曲始终显示红心
            .duration-actions .like-btn.liked {
              opacity: 1;
            }
          }
        }
      }
    }
  }
}
</style>
