<template>
    <transition name="fullscreen-fade">
        <div class="fullscreen-login" v-if="visible" @click.self="close">
            <div class="fullscreen-login-content" @click.self="close">
                <!-- 顶部控制栏 -->
                <div class="fullscreen-controls-bar">
                    <!-- 返回按钮 -->
                    <div class="back-button" @click="close">
                        <button class="back-btn">
                            <close-outlined />
                        </button>
                        <span class="back-text">关闭</span>
                    </div>

                    <!-- 标题 -->
                    <div class="login-title">
                        <h1>网易云音乐登录</h1>
                    </div>
                </div>

                <!-- 主要内容区域 -->
                <div class="fullscreen-main" @click.self="close">
                    <!-- 左右分栏布局 -->
                    <div class="login-container" @click.stop>
                        <!-- 左侧二维码区域 -->
                        <div class="qr-section">
                            <div class="qr-code">
                                <div class="qr-placeholder">
                                    <!-- 二维码占位符 -->
                                    <div class="qr-grid">
                                        <div v-for="i in 100" :key="i" class="qr-dot" :class="{ active: Math.random() > 0.5 }"></div>
                                    </div>
                                </div>
                            </div>
                            <p class="qr-tip">使用网易云音乐APP扫码登录</p>

                            <!-- 扫描状态 -->
                            <div class="scan-status">
                                <div class="status-indicator" :class="scanStatus">
                                    <div class="status-dot"></div>
                                    <span class="status-text">{{ getScanStatusText() }}</span>
                                </div>
                                <div class="scan-actions" v-if="scanStatus === 'expired'">
                                    <a @click="refreshQRCode" class="refresh-link">点击刷新</a>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧表单区域 -->
                        <div class="form-section">
                                <a-form layout="vertical" class="login-form">
                                    <a-form-item class="form-item">
                                        <a-input
                                            v-model:value="phoneNumber"
                                            placeholder="请输入手机号"
                                            class="custom-input"
                                            size="large"
                                            maxlength="11"
                                        />
                                    </a-form-item>

                                    <a-form-item class="form-item">
                                        <div class="verification-input">
                                            <a-input
                                                v-model:value="verificationCode"
                                                placeholder="请输入验证码"
                                                class="custom-input code-input"
                                                size="large"
                                                maxlength="6"
                                            />
                                            <a-button
                                                class="send-code-btn"
                                                :disabled="!canSendCode || countdown > 0"
                                                @click="sendVerificationCode"
                                                :loading="sendingCode"
                                            >
                                                {{ getCodeButtonText() }}
                                            </a-button>
                                        </div>
                                    </a-form-item>

                                    <div class="form-options">
                                        <a class="forgot-link" @click="switchToPassword">密码登录</a>
                                    </div>

                                    <a-button
                                        type="primary"
                                        block
                                        size="large"
                                        class="login-btn"
                                        @click="handleLogin"
                                        :loading="loginLoading"
                                    >
                                        <span v-if="!loginLoading">登录</span>
                                    </a-button>

                                    <!-- 底部链接 -->
                                    <div class="bottom-links">
                                        <a class="link" @click="guestLogin">游客登录</a>
                                    </div>

                                    <!-- 第三方登录 -->
                                    <div class="social-login">
                                        <div class="social-buttons">
                                            <div class="social-btn wechat" title="微信登录">
                                                <wechat-outlined />
                                            </div>
                                            <div class="social-btn qq" title="QQ登录">
                                                <qq-outlined />
                                            </div>
                                            <div class="social-btn weibo" title="微博登录">
                                                <weibo-outlined />
                                            </div>
                                        </div>
                                    </div>
                            </a-form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </transition>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue';
import { message } from 'ant-design-vue';
import {
    CloseOutlined,
    WechatOutlined,
    QqOutlined,
    WeiboOutlined
} from '@ant-design/icons-vue';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['close', 'login-success']);

// 表单数据
const phoneNumber = ref('');
const verificationCode = ref('');
const loginLoading = ref(false);

// 验证码相关
const sendingCode = ref(false);
const countdown = ref(0);
const canSendCode = ref(true);

// 二维码扫描状态
const scanStatus = ref('waiting'); // waiting, scanned, expired

// 关闭模态框
function close() {
    emit('close');
}

// 处理登录
async function handleLogin() {
    if (!phoneNumber.value || !verificationCode.value) {
        message.warning('请输入手机号和验证码');
        return;
    }

    // 简单的手机号验证
    if (!/^1[3-9]\d{9}$/.test(phoneNumber.value)) {
        message.warning('请输入正确的手机号');
        return;
    }

    loginLoading.value = true;

    try {
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 1500));

        // 模拟登录成功
        const userInfo = {
            username: phoneNumber.value,
            loginTime: new Date().toISOString(),
            avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${phoneNumber.value}`
        };

        // 发出登录成功事件
        emit('login-success', userInfo);

        // 清空表单
        phoneNumber.value = '';
        verificationCode.value = '';

        message.success('登录成功！');

        // 延迟关闭模态框
        setTimeout(() => {
            close();
        }, 800);

    } catch (error) {
        message.error('登录失败，请重试');
    } finally {
        loginLoading.value = false;
    }
}

// 发送验证码（演示）
async function sendVerificationCode() {
    if (!phoneNumber.value) {
        message.warning('请先输入手机号');
        return;
    }

    sendingCode.value = true;

    try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        message.success('验证码已发送');

        // 开始倒计时
        countdown.value = 60;
        canSendCode.value = false;

        const timer = setInterval(() => {
            countdown.value--;
            if (countdown.value <= 0) {
                clearInterval(timer);
                canSendCode.value = true;
            }
        }, 1000);

    } catch (error) {
        message.error('发送失败，请重试');
    } finally {
        sendingCode.value = false;
    }
}

// 获取验证码按钮文字
function getCodeButtonText() {
    if (sendingCode.value) return '发送中...';
    if (countdown.value > 0) return `${countdown.value}s`;
    return '获取验证码';
}

// 切换到密码登录
function switchToPassword() {
    message.info('密码登录功能开发中...');
}

// 获取扫描状态文字
function getScanStatusText() {
    switch (scanStatus.value) {
        case 'waiting':
            return '等待扫描';
        case 'scanned':
            return '扫描成功，请在手机上确认';
        case 'expired':
            return '二维码已过期';
        default:
            return '等待扫描';
    }
}



// 刷新二维码
function refreshQRCode() {
    scanStatus.value = 'waiting';
    message.success('二维码已刷新');
}

// 游客登录
async function guestLogin() {
    loginLoading.value = true;

    try {
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 模拟游客登录成功
        const guestInfo = {
            username: '游客用户',
            loginTime: new Date().toISOString(),
            avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=guest',
            isGuest: true
        };

        // 发出登录成功事件
        emit('login-success', guestInfo);

        message.success('游客登录成功！');

        // 延迟关闭模态框
        setTimeout(() => {
            close();
        }, 800);

    } catch (error) {
        message.error('游客登录失败，请重试');
    } finally {
        loginLoading.value = false;
    }
}
</script>

<style lang="scss" scoped>
@import '../styles/design-system.scss';

// 动画定义
.fullscreen-fade-enter-active,
.fullscreen-fade-leave-active {
  transition: opacity 0.3s ease-in-out;
}

.fullscreen-fade-enter-from,
.fullscreen-fade-leave-to {
  opacity: 0;
}

// 全屏登录页面
.fullscreen-login {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(20px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-in-out;

  // 禁止文字选中和图片拖拽
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  // 禁止所有元素拖拽
  * {
      -webkit-user-drag: none;
      -khtml-user-drag: none;
      -moz-user-drag: none;
      -o-user-drag: none;
      pointer-events: auto; // 保持点击事件
  }

  // 特别针对图片禁止拖拽
  img {
      -webkit-user-drag: none;
      -khtml-user-drag: none;
      -moz-user-drag: none;
      -o-user-drag: none;
      pointer-events: none; // 图片完全禁止交互
  }

  .fullscreen-login-content {
    width: 100%;
    max-width: 1200px;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: $spacing-xl;
    position: relative;

    // 顶部控制栏
    .fullscreen-controls-bar {
      position: absolute;
      top: $spacing-xl;
      left: $spacing-xl;
      right: $spacing-xl;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10;

      .back-button {
        position: absolute;
        right: 0;
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        cursor: pointer;
        @include transition();

        &:hover {
          transform: translateX(4px);
        }

        .back-btn {
          width: 48px;
          height: 48px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          @include flex-center;
          cursor: pointer;
          @include transition();
          color: var(--text-primary);

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
          }

          .anticon {
            font-size: 20px;
          }
        }

        .back-text {
          color: var(--text-primary);
          font-size: $font-size-lg;
          font-weight: $font-weight-medium;
        }
      }

      .login-title {
        h1 {
          color: var(--text-primary);
          font-size: $font-size-xxxl;
          font-weight: $font-weight-bold;
          margin: 0;
          text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }
      }
    }

    // 主要内容区域
    .fullscreen-main {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: $spacing-xxl;

      .login-container {
        display: flex;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        box-shadow:
          0 32px 64px rgba(0, 0, 0, 0.2),
          0 16px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        max-width: 900px;
        width: 100%;
        min-height: 500px;

        // 左侧二维码区域
        .qr-section {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 60px 40px;
          background: linear-gradient(135deg, #f8f9fa, #e9ecef);
          border-right: 1px solid rgba(0, 0, 0, 0.1);

          .qr-code {
            width: 200px;
            height: 200px;
            background: white;
            border: 2px solid #e8e8e8;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 24px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);

            .qr-placeholder {
              width: 100%;
              height: 100%;
              position: relative;

              .qr-grid {
                display: grid;
                grid-template-columns: repeat(10, 1fr);
                grid-template-rows: repeat(10, 1fr);
                gap: 1px;
                width: 100%;
                height: 100%;

                .qr-dot {
                  background: #f5f5f5;
                  border-radius: 1px;

                  &.active {
                    background: #333;
                  }
                }
              }
            }
          }

          .qr-tip {
            color: #666;
            font-size: 16px;
            text-align: center;
            margin: 0;
            line-height: 1.4;
            font-weight: 500;
          }

          .scan-status {
            margin-top: 16px;
            text-align: center;

            .status-indicator {
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 8px;
              margin-bottom: 8px;

              .status-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: #52c41a;
              }

              .status-text {
                font-size: 12px;
                color: #666;
              }

              &.waiting .status-dot {
                background: #faad14;
                animation: pulse 2s infinite;
              }

              &.scanned .status-dot {
                background: #52c41a;
              }

              &.expired .status-dot {
                background: #ff4d4f;
              }
            }

            .scan-actions {
              .refresh-link {
                color: #1890ff;
                font-size: 12px;
                cursor: pointer;
                text-decoration: none;

                &:hover {
                  text-decoration: underline;
                }
              }
            }
          }
        }

        // 右侧表单区域
        .form-section {
          flex: 1;
          padding: 60px 40px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          background: #ffffff;
        }
      }
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 表单样式
.login-form {
  .form-item {
    margin-bottom: 16px;

    .verification-input {
      display: flex;
      gap: 8px;

      .code-input {
        flex: 1;
      }

      .send-code-btn {
        width: 100px;
        height: 44px;
        border-radius: 6px;
        font-size: 13px;
        border: 1px solid #d9d9d9;
        background: #fff;
        color: #666;

        &:hover:not(:disabled) {
          border-color: #1890ff;
          color: #1890ff;
        }

        &:disabled {
          background: #f5f5f5;
          color: #bfbfbf;
          cursor: not-allowed;
        }
      }
    }

    :deep(.custom-input) {
      .ant-input,
      .ant-input-password {
        background: #ffffff;
        border: 1px solid #e1e5e9;
        border-radius: 6px;
        color: #333;
        padding: 12px 16px;
        font-size: 15px;
        height: 44px;
        @include transition();

        &::placeholder {
          color: #8c8c8c;
          opacity: 1;
        }

        &:hover {
          border-color: #1890ff;
        }

        &:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
          outline: none;
        }
      }

      .ant-input-password-icon {
        color: #8c8c8c;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }

  .form-options {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;

    .forgot-link {
      color: #1890ff;
      font-size: 14px;
      text-decoration: none;
      cursor: pointer;
      @include transition(color);

      &:hover {
        color: #40a9ff;
        text-decoration: underline;
      }
    }
  }

  .login-btn {
    background: #ec4141;
    border: none;
    border-radius: 24px;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
    @include transition();

    &:hover {
      background: #ff6b6b;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(236, 65, 65, 0.3);
    }

    &:active {
      transform: translateY(0);
    }

    :deep(.ant-btn-loading-icon) {
      color: white;
    }
  }

  .bottom-links {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-bottom: 24px;

    .link {
      color: #1890ff;
      font-size: 14px;
      text-decoration: none;
      cursor: pointer;
      @include transition(color);

      &:hover {
        color: #40a9ff;
        text-decoration: underline;
      }
    }
  }

  .social-login {
    text-align: center;

    .social-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;

      .social-btn {
        width: 40px;
        height: 40px;
        background: #f5f5f5;
        border: 1px solid #e8e8e8;
        border-radius: 50%;
        @include flex-center;
        cursor: pointer;
        @include transition();

        .anticon {
          font-size: 18px;
        }

        &:hover {
          background: #fff;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        &.wechat {
          .anticon {
            color: #07c160;
          }

          &:hover {
            border-color: #07c160;
          }
        }

        &.qq {
          .anticon {
            color: #12b7f5;
          }

          &:hover {
            border-color: #12b7f5;
          }
        }

        &.weibo {
          .anticon {
            color: #e6162d;
          }

          &:hover {
            border-color: #e6162d;
          }
        }
      }
    }
  }
}

// 响应式设计
@include respond-to(xs) {
  .fullscreen-login {
    .fullscreen-login-content {
      padding: $spacing-md;

      .fullscreen-controls-bar {
        top: $spacing-md;
        left: $spacing-md;
        right: $spacing-md;
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-md;

        .login-title h1 {
          font-size: $font-size-xxl;
        }
      }

      .fullscreen-main {
        padding-top: $spacing-xl;

        .login-container {
          flex-direction: column;
          max-width: 100%;
          min-height: auto;

          .qr-section {
            border-right: none;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding: 40px 24px;

            .qr-code {
              width: 160px;
              height: 160px;
              padding: 16px;
            }
          }

          .form-section {
            padding: 40px 24px;
          }
        }
      }

      .social-login .social-buttons {
        gap: 16px;

        .social-btn {
          width: 36px;
          height: 36px;

          .anticon {
            font-size: 16px;
          }
        }
      }
    }
  }
}
</style>