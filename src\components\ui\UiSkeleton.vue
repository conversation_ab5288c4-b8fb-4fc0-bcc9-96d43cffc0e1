<template>
  <div :class="skeletonClasses" :style="skeletonStyles">
    <div v-if="avatar" class="skeleton-avatar" :class="avatarClasses"></div>
    <div v-if="title || paragraph" class="skeleton-content">
      <div v-if="title" class="skeleton-title" :style="titleStyles"></div>
      <div v-if="paragraph" class="skeleton-paragraph">
        <div 
          v-for="(line, index) in paragraphLines" 
          :key="index"
          class="skeleton-line"
          :style="{ width: line }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  // 是否显示头像
  avatar: {
    type: Boolean,
    default: false
  },
  // 头像形状
  avatarShape: {
    type: String,
    default: 'circle',
    validator: (value) => ['circle', 'square'].includes(value)
  },
  // 头像尺寸
  avatarSize: {
    type: [String, Number],
    default: 'default'
  },
  // 是否显示标题
  title: {
    type: Boolean,
    default: true
  },
  // 标题宽度
  titleWidth: {
    type: [String, Number],
    default: '40%'
  },
  // 是否显示段落
  paragraph: {
    type: Boolean,
    default: true
  },
  // 段落行数
  paragraphRows: {
    type: Number,
    default: 3
  },
  // 段落行宽度
  paragraphWidth: {
    type: Array,
    default: () => ['100%', '80%', '60%']
  },
  // 是否激活动画
  active: {
    type: Boolean,
    default: true
  },
  // 自定义宽度
  width: {
    type: [String, Number],
    default: null
  },
  // 自定义高度
  height: {
    type: [String, Number],
    default: null
  },
  // 圆角
  round: {
    type: Boolean,
    default: false
  }
});

const skeletonClasses = computed(() => [
  'ui-skeleton',
  {
    'ui-skeleton--active': props.active,
    'ui-skeleton--round': props.round
  }
]);

const skeletonStyles = computed(() => {
  const styles = {};
  if (props.width) {
    styles.width = typeof props.width === 'number' ? `${props.width}px` : props.width;
  }
  if (props.height) {
    styles.height = typeof props.height === 'number' ? `${props.height}px` : props.height;
  }
  return styles;
});

const avatarClasses = computed(() => [
  'skeleton-avatar',
  `skeleton-avatar--${props.avatarShape}`,
  `skeleton-avatar--${props.avatarSize}`
]);

const titleStyles = computed(() => ({
  width: typeof props.titleWidth === 'number' ? `${props.titleWidth}px` : props.titleWidth
}));

const paragraphLines = computed(() => {
  const lines = [];
  for (let i = 0; i < props.paragraphRows; i++) {
    if (props.paragraphWidth[i]) {
      lines.push(props.paragraphWidth[i]);
    } else {
      // 默认宽度模式
      if (i === props.paragraphRows - 1) {
        lines.push('60%'); // 最后一行较短
      } else {
        lines.push('100%');
      }
    }
  }
  return lines;
});
</script>

<style lang="scss" scoped>
@import '../../styles/design-system.scss';

.ui-skeleton {
  display: flex;
  gap: $spacing-md;
  
  &--active {
    .skeleton-element {
      @include loading-shimmer;
    }
  }
  
  &--round {
    .skeleton-element {
      border-radius: $border-radius-md;
    }
  }
}

.skeleton-element {
  background: rgba(255, 255, 255, 0.1);
  border-radius: $border-radius-sm;
}

.skeleton-avatar {
  @extend .skeleton-element;
  flex-shrink: 0;
  
  &--circle {
    border-radius: 50%;
  }
  
  &--square {
    border-radius: $border-radius-sm;
  }
  
  // 尺寸
  &--small {
    width: 24px;
    height: 24px;
  }
  
  &--default {
    width: 40px;
    height: 40px;
  }
  
  &--large {
    width: 64px;
    height: 64px;
  }
}

.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.skeleton-title {
  @extend .skeleton-element;
  height: 16px;
}

.skeleton-paragraph {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.skeleton-line {
  @extend .skeleton-element;
  height: 14px;
}

// 预设骨架屏样式
.skeleton-song-item {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  padding: $spacing-sm;
  
  .skeleton-cover {
    @extend .skeleton-element;
    width: 48px;
    height: 48px;
    border-radius: $border-radius-sm;
  }
  
  .skeleton-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: $spacing-xs;
    
    .skeleton-title {
      @extend .skeleton-element;
      height: 16px;
      width: 60%;
    }
    
    .skeleton-artist {
      @extend .skeleton-element;
      height: 14px;
      width: 40%;
    }
  }
  
  .skeleton-duration {
    @extend .skeleton-element;
    width: 40px;
    height: 14px;
  }
}

.skeleton-playlist-card {
  @extend .skeleton-element;
  width: 200px;
  height: 280px;
  padding: $spacing-md;
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
  
  .skeleton-cover {
    @extend .skeleton-element;
    width: 100%;
    height: 160px;
    border-radius: $border-radius-md;
  }
  
  .skeleton-title {
    @extend .skeleton-element;
    height: 16px;
    width: 80%;
  }
  
  .skeleton-description {
    @extend .skeleton-element;
    height: 14px;
    width: 60%;
  }
}

.skeleton-player-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-lg;
  padding: $spacing-xxl;
  
  .skeleton-album-cover {
    @extend .skeleton-element;
    width: 300px;
    height: 300px;
    border-radius: $border-radius-lg;
  }
  
  .skeleton-song-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: $spacing-sm;
    
    .skeleton-title {
      @extend .skeleton-element;
      height: 24px;
      width: 200px;
    }
    
    .skeleton-artist {
      @extend .skeleton-element;
      height: 16px;
      width: 120px;
    }
  }
  
  .skeleton-lyrics {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: $spacing-sm;
    
    .skeleton-line {
      @extend .skeleton-element;
      height: 20px;
      
      &:first-child {
        width: 250px;
      }
      
      &:last-child {
        width: 180px;
      }
    }
  }
}
</style>
