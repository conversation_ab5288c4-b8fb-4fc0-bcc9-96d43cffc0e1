<template>
  <div class="sidebar">
    <div class="logo">
      <span class="logo-icon">
        <customer-service-outlined />
      </span>
      <span class="logo-text">网易云音乐</span>
    </div>

    <div class="menu-container">
      <div class="menu-item" :class="{ active: activeView === 'discover' }" @click="navigateTo('/discover')">
        <div class="menu-btn">
          <play-circle-outlined class="menu-icon" />
          <span>推荐</span>
        </div>
      </div>
      <div class="menu-item" :class="{ active: activeView === 'newSongs' }" @click="navigateTo('/new-songs')">
        <div class="menu-btn">
          <search-outlined class="menu-icon" />
          <span>新歌模式</span>
        </div>
      </div>
    </div>

    <div class="section-title">我的音乐</div>

    <div class="menu-container">
      <div class="menu-item"
           :class="{ active: activeView === 'favoriteMusic', 'login-required': !isLoggedIn }"
           @click="navigateTo('/my-music/favorite')">
        <div class="menu-btn">
          <heart-outlined class="menu-icon" />
          <span>我喜欢的音乐</span>
          <span class="lock-icon" v-if="!isLoggedIn"></span>
        </div>
      </div>
      <div class="menu-item"
           :class="{ active: activeView === 'collectedPlaylists', 'login-required': !isLoggedIn }"
           @click="navigateTo('/my-music/playlists')">
        <div class="menu-btn">
          <clock-circle-outlined class="menu-icon" />
          <span>我收藏的歌单</span>
          <span class="lock-icon" v-if="!isLoggedIn"></span>
        </div>
      </div>
    </div>

    <!-- 播放列表展示区域 -->
    <div class="playlist-display">
      <div class="playlist-header">
        <span class="playlist-title">播放列表</span>
        <span class="playlist-count">({{ musicStore.playlist.length }})</span>
      </div>

      <div class="playlist-content">
        <div
          v-for="(song, index) in musicStore.playlist"
          :key="song.id || index"
          class="playlist-item"
          :class="{ active: isCurrentSong(song, index) }"
          @click="playSong(song, index)"
        >
          <div class="song-index">{{ index + 1 }}</div>
          <div class="song-info">
            <div class="song-name">{{ song.title || song.name || `歌曲 ${index + 1}` }}</div>
            <div class="song-artist">{{ song.artist || '未知艺术家' }}</div>
          </div>
          <div class="song-duration">{{ song.duration || '3:45' }}</div>
          <div class="delete-btn" @click.stop="removeSong(index)" title="删除">
            ×
          </div>
        </div>

        <div v-if="musicStore.playlist.length === 0" class="empty-playlist">
          暂无播放列表
        </div>
      </div>
    </div>

    <!-- 播放控制按钮 -->
    <div class="playback-controls">
      <div class="control-icon" @click="prevSong">
        <step-backward-outlined />
      </div>

      <div class="control-icon play-btn" @click="togglePlay">
        <pause-outlined v-if="isPlaying" />
        <caret-right-outlined v-else />
      </div>

      <div class="control-icon" @click="nextSong">
        <step-forward-outlined />
      </div>
    </div>
  </div>
</template>

<script setup>
// 侧边栏功能
// --------------------导入Ant Design图标--------------------
import {
  CustomerServiceOutlined,
  PlayCircleOutlined,
  SearchOutlined,
  HeartOutlined,
  ClockCircleOutlined,
  HistoryOutlined,
  PlusOutlined,
  StepBackwardOutlined,
  StepForwardOutlined,
  PauseOutlined,
  CaretRightOutlined
} from '@ant-design/icons-vue';

import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMusicStore } from '../stores/music';

const route = useRoute();
const router = useRouter();
const musicStore = useMusicStore();

// 接收属性
const props = defineProps({
  isLoggedIn: {
    type: Boolean,
    default: false
  }
});

// 播放状态
const isPlaying = computed(() => musicStore.isPlaying);

// 计算当前激活的菜单项
const activeView = computed(() => {
  if (!route.name) return 'discover';

  const routeName = route.name;
  switch (routeName) {
    case 'Discover':
      return 'discover';
    case 'NewSongs':
      return 'newSongs';
    case 'FavoriteMusic':
      return 'favoriteMusic';
    case 'CollectedPlaylists':
      return 'collectedPlaylists';
    default:
      return 'discover';
  }
});

// 路由导航方法
function navigateTo(path) {
  router.push(path);
}

// 播放控制方法
function togglePlay() {
  musicStore.togglePlay();
}

function nextSong() {
  musicStore.nextSong();
}

function prevSong() {
  musicStore.prevSong();
}

// 播放指定歌曲
async function playSong(song, index) {
  try {
    await musicStore.playSong(song);
  } catch (error) {
    console.error('播放歌曲失败:', error);
  }
}

// 判断是否为当前播放歌曲
function isCurrentSong(song, index) {
  const currentSong = musicStore.currentSong;
  if (!currentSong) return false;
  return currentSong.id === song.id ||
         (currentSong.title === song.title && currentSong.artist === song.artist);
}

// 删除歌曲
function removeSong(index) {
  if (musicStore.playlist.length <= 1) {
    // 如果只有一首歌，清空播放列表
    musicStore.clearPlaylist();
  } else {
    // 删除指定歌曲
    musicStore.removeSongFromPlaylist(index);
  }
}
</script>

<style lang="scss" scoped>
.sidebar {
  width: var(--sidebar-width);
  height: 100%;
  background-color: #3a4049;
  color: #fff;
  padding: 15px 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;

  // 禁止文字选中和图片拖拽
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  // 禁止所有元素拖拽
  * {
      -webkit-user-drag: none;
      -khtml-user-drag: none;
      -moz-user-drag: none;
      -o-user-drag: none;
      pointer-events: auto; // 保持点击事件
  }

  // 特别针对图片禁止拖拽
  img {
      -webkit-user-drag: none;
      -khtml-user-drag: none;
      -moz-user-drag: none;
      -o-user-drag: none;
      pointer-events: none; // 图片完全禁止交互
  }

  .logo {
    display: flex;
    align-items: center;
    margin-bottom: 25px;

    .logo-icon {
      font-size: 18px;
      margin-right: 6px;
    }

    .logo-text {
      font-size: 15px;
      font-weight: bold;
    }
  }

  .section-title {
    font-size: 11px;
    color: #aaa;
    margin: 16px 0 8px 6px;
  }

  .menu-container {
    .menu-item {
      margin-bottom: 4px;
      cursor: pointer;

      &.active .menu-btn {
        background-color: rgba(255, 255, 255, 0.1);
      }
      
      &.login-required .menu-btn {
        position: relative;
        color: rgba(255, 255, 255, 0.7);
      }

      .menu-btn {
        display: flex;
        align-items: center;
        padding: 5px 8px;
        border-radius: 4px;
        color: #fff;
        font-size: 12px;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        .menu-icon {
          margin-right: 8px;
          font-size: 14px;
        }
        
        .lock-icon {
          font-size: 10px;
          margin-left: 5px;
        }
      }
    }
  }

  // 播放控制按钮
  .playback-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    padding: 16px 8px;

    .control-icon {
      color: #fff;
      cursor: pointer;
      font-size: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        color: #1890ff;
      }

      &:active {
        transform: translateY(0);
      }

      &.play-btn {
        font-size: 20px;
        width: 42px;
        height: 42px;
        background: linear-gradient(135deg, #ec4141, #ff6b6b);
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow:
          0 4px 16px rgba(236, 65, 65, 0.3),
          0 2px 8px rgba(236, 65, 65, 0.2);

        &:hover {
          background: linear-gradient(135deg, #ff6b6b, #ec4141);
          transform: translateY(-3px) scale(1.05);
          box-shadow:
            0 6px 20px rgba(236, 65, 65, 0.4),
            0 3px 12px rgba(236, 65, 65, 0.3);
          color: white;
        }
      }
    }
  }

  // 播放列表展示区域
  .playlist-display {
    max-height: 550px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    // border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .playlist-header {
      padding: 12px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      // border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 20px;

      .playlist-title {
        font-size: 12px;
        color: #fff;
        font-weight: 600;
      }

      .playlist-count {
        font-size: 11px;
        color: #aaa;
      }
    }

    .playlist-content {
      flex: 1;
      overflow-y: auto;
      padding: 4px 0;

      .playlist-item {
        display: flex;
        align-items: center;
        padding: 6px 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        border-radius: 4px;
        margin: 1px 4px;
        position: relative;

        &:hover {
          background: rgba(255, 255, 255, 0.08);

          .delete-btn {
            opacity: 1;
          }
        }

        &.active {
          background: rgba(24, 144, 255, 0.25);
          border-left: 3px solid #1890ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);

          .song-name {
            color: #69c0ff;
            font-weight: 600;
          }
        }

        .song-index {
          width: 20px;
          font-size: 10px;
          color: #aaa;
          text-align: center;
        }

        .song-info {
          flex: 1;
          margin-left: 8px;
          min-width: 0;

          .song-name {
            font-size: 11px;
            color: #fff;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.3;
          }

          .song-artist {
            font-size: 10px;
            color: #aaa;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.2;
            margin-top: 1px;
          }
        }

        .song-duration {
          font-size: 10px;
          color: #aaa;
          margin-left: 4px;
        }

        .delete-btn {
          position: absolute;
          right: 6px;
          width: 20px;
          height: 20px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          color: #ff4d4f;
          cursor: pointer;
          opacity: 0;
          transition: all 0.2s ease;

          &:hover {
            background: rgba(255, 77, 79, 0.2);
            transform: scale(1.1);
          }
        }
      }

      .more-songs {
        padding: 8px;
        text-align: center;
        font-size: 10px;
        color: #aaa;
        border-top: 1px solid rgba(255, 255, 255, 0.05);
      }

      .empty-playlist {
        padding: 20px 8px;
        text-align: center;
        font-size: 11px;
        color: #666;
      }

      // 自定义滚动条
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 2px;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
}
</style>