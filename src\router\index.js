import { createRouter, createWebHistory } from 'vue-router'

// 导入页面组件
import PlayerContent from '../components/PlayerContent.vue'
import NewSongMode from '../components/NewSongMode.vue'
import FavoriteMusic from '../components/FavoriteMusic.vue'
import CollectedPlaylists from '../components/CollectedPlaylists.vue'
import PlaylistDetail from '../components/PlaylistDetail.vue'
import SongDetailTest from '../components/SongDetailTest.vue'
import SongUrlTest from '../components/SongUrlTest.vue'
import PlaySystemTest from '../components/PlaySystemTest.vue'

const routes = [
  {
    path: '/',
    redirect: '/discover'
  },
  {
    path: '/discover',
    name: 'Discover',
    component: PlayerContent,
    meta: {
      title: '发现音乐',
      breadcrumb: [
        { title: '首页', path: '/discover' }
      ]
    }
  },
  {
    path: '/new-songs',
    name: 'NewSongs',
    component: NewSongMode,
    meta: {
      title: '新歌速递',
      breadcrumb: [
        { title: '首页', path: '/discover' },
        { title: '新歌速递', path: '/new-songs' }
      ]
    }
  },
  {
    path: '/my-music',
    name: 'MyMusic',
    redirect: '/my-music/favorite',
    meta: {
      title: '我的音乐',
      breadcrumb: [
        { title: '首页', path: '/discover' },
        { title: '我的音乐', path: '/my-music' }
      ]
    }
  },
  {
    path: '/my-music/favorite',
    name: 'FavoriteMusic',
    component: FavoriteMusic,
    meta: {
      title: '我喜欢的音乐',
      breadcrumb: [
        { title: '首页', path: '/discover' },
        { title: '我的音乐', path: '/my-music' },
        { title: '我喜欢的音乐', path: '/my-music/favorite' }
      ]
    }
  },
  {
    path: '/my-music/playlists',
    name: 'CollectedPlaylists',
    component: CollectedPlaylists,
    meta: {
      title: '我收藏的歌单',
      breadcrumb: [
        { title: '首页', path: '/discover' },
        { title: '我的音乐', path: '/my-music' },
        { title: '我收藏的歌单', path: '/my-music/playlists' }
      ]
    }
  },
  {
    path: '/playlist/:id',
    name: 'PlaylistDetail',
    component: PlaylistDetail,
    meta: {
      title: '歌单详情',
      breadcrumb: [
        { title: '首页', path: '/discover' },
        { title: '歌单详情', path: '' }
      ]
    }
  },
  {
    path: '/search',
    name: 'Search',
    component: () => import('../components/SearchResults.vue'),
    meta: {
      title: '搜索结果',
      breadcrumb: [
        { title: '首页', path: '/discover' },
        { title: '搜索结果', path: '/search' }
      ]
    }
  },
  {
    path: '/test/song-detail',
    name: 'SongDetailTest',
    component: SongDetailTest,
    meta: {
      title: '歌曲详情API测试',
      breadcrumb: [
        { title: '首页', path: '/discover' },
        { title: 'API测试', path: '/test/song-detail' }
      ]
    }
  },
  {
    path: '/test/song-url',
    name: 'SongUrlTest',
    component: SongUrlTest,
    meta: {
      title: '音乐URL获取API测试',
      breadcrumb: [
        { title: '首页', path: '/discover' },
        { title: 'API测试', path: '/test/song-url' }
      ]
    }
  },
  {
    path: '/test/play-system',
    name: 'PlaySystemTest',
    component: PlaySystemTest,
    meta: {
      title: '播放系统测试',
      breadcrumb: [
        { title: '首页', path: '/discover' },
        { title: '播放系统测试', path: '/test/play-system' }
      ]
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/discover'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = `${to.meta.title} - 网易云音乐`
  }
  next()
})

export default router
