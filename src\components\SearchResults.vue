<template>
  <div class="search-results">
    <div class="search-header">
      <h1>搜索结果</h1>
      <p>搜索功能正在开发中...</p>
    </div>
  </div>
</template>

<script setup>
// 搜索结果页面
</script>

<style lang="scss" scoped>
@import '../styles/design-system.scss';

.search-results {
  height: 100%;
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  color: var(--text-primary);
  padding: $spacing-xl;

  .search-header {
    text-align: center;
    padding: $spacing-xxl;

    h1 {
      font-size: $font-size-xxxl;
      margin-bottom: $spacing-md;
    }

    p {
      color: var(--text-secondary);
      font-size: $font-size-lg;
    }
  }
}
</style>
