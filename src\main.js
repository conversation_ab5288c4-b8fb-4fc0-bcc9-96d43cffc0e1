import { createApp } from 'vue'
import { createPinia } from 'pinia'
import Antd from 'ant-design-vue'
import router from './router'
import App from './App.vue'

// 导入 Ant Design Vue 样式
import 'ant-design-vue/dist/reset.css'

// 导入全局样式
import './styles/design-system.scss'

// 创建 Vue 应用实例
const app = createApp(App)

// 创建 Pinia 实例
const pinia = createPinia()

// 配置全局属性
app.config.globalProperties.$ELEMENT = { size: 'small', zIndex: 3000 }

// 错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err)
  console.error('Component:', vm)
  console.error('Error Info:', info)
}

// 警告处理（仅在开发环境）
if (import.meta.env.DEV) {
  app.config.warnHandler = (msg, vm, trace) => {
    console.warn('Vue Warning:', msg)
    console.warn('Component:', vm)
    console.warn('Trace:', trace)
  }
}

// 使用插件
app.use(pinia)
app.use(router)
app.use(Antd)

// 挂载应用
app.mount('#app')
