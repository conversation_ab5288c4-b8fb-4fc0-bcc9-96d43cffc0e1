# 音乐 API 使用文档

## 概述

已在 `useMusicStore` 中实现了完整的音乐相关API功能：
- 获取歌曲详情
- 获取音乐播放URL（支持新旧两版接口）
- 检查音乐可用性

## API 接口

### 1. 歌曲详情接口
```
GET /song/detail?ids={歌曲ID}
```

### 2. 音乐URL接口（旧版）
```
GET /song/url?id={歌曲ID}&br={码率}
```

### 3. 音乐URL接口（新版）
```
GET /song/url/v1?id={歌曲ID}&level={音质等级}
```

### 4. 音乐可用性检查
```
GET /check/music?id={歌曲ID}&br={码率}
```

## Store 方法

### 1. `fetchSongDetail(ids)`

获取歌曲详情的主要方法。

#### 参数
- `ids` (string|number|Array): 歌曲ID，支持以下格式：
  - 单个ID：`347230`
  - ID数组：`[347230, 347231]`
  - 字符串：`"347230,347231"`

#### 返回值
- 单个ID：返回格式化的歌曲对象
- 多个ID：返回格式化的歌曲对象数组

### 2. `fetchSongUrl(ids, br)`

获取音乐播放URL（旧版接口）。

#### 参数
- `ids` (string|number|Array): 歌曲ID
- `br` (number): 码率，默认999000（最大码率）
  - 128000: 128k
  - 192000: 192k
  - 320000: 320k
  - 999000: 最高码率

### 3. `fetchSongUrlV1(ids, level)`

获取音乐播放URL（新版接口，推荐使用）。

#### 参数
- `ids` (string|number|Array): 歌曲ID
- `level` (string): 音质等级，默认'exhigh'
  - `standard`: 标准
  - `higher`: 较高
  - `exhigh`: 极高
  - `lossless`: 无损
  - `hires`: Hi-Res
  - `jyeffect`: 高清环绕声
  - `sky`: 沉浸环绕声
  - `dolby`: 杜比全景声
  - `jymaster`: 超清母带

### 4. `checkMusicAvailable(id, br)`

检查音乐是否可用。

#### 参数
- `id` (string|number): 歌曲ID
- `br` (number): 码率，默认999000

#### 使用示例

```javascript
import { useMusicStore } from '@/stores/music'

const musicStore = useMusicStore()

// 获取歌曲详情
try {
  const song = await musicStore.fetchSongDetail('347230')
  console.log('歌曲详情:', song)
} catch (error) {
  console.error('获取失败:', error)
}

// 获取播放URL（新版）
try {
  const urlData = await musicStore.fetchSongUrlV1('347230', 'exhigh')
  console.log('播放URL:', urlData.url)
} catch (error) {
  console.error('获取URL失败:', error)
}

// 检查可用性
try {
  const availability = await musicStore.checkMusicAvailable('347230')
  console.log('可用性:', availability.available)
} catch (error) {
  console.error('检查失败:', error)
}
```

## 数据结构

### 格式化后的歌曲数据结构

```javascript
{
  id: 347230,                           // 歌曲ID
  title: "海阔天空",                     // 歌曲标题
  artist: "Beyond",                     // 歌手名称（多个歌手用 / 分隔）
  artistList: [                         // 歌手列表
    { id: 11127, name: "Beyond" }
  ],
  album: "海阔天空",                     // 专辑名称
  albumId: 34209,                       // 专辑ID
  coverUrl: "https://...",              // 封面图片URL
  duration: 326000,                     // 时长（毫秒）
  durationText: "05:26",                // 格式化时长
  
  // 音质信息
  qualities: {
    hr: null,                           // Hi-Res音质
    sq: { br: 797831, size: 32511640 }, // 无损音质
    h: { br: 320001, size: 13042460 },  // 高品质
    m: { br: 192001, size: 7825493 },   // 中品质
    l: { br: 128001, size: 5217010 }    // 低品质
  },
  
  // 权限信息
  privilege: {
    canPlay: true,                      // 是否可播放
    canDownload: false,                 // 是否可下载
    maxBitrate: 999000,                 // 最高码率
    fee: 1,                            // 收费类型
    toast: false                        // 是否有地区限制
  },
  
  // 其他信息
  mvId: 376199,                         // MV ID
  hasMv: true,                          // 是否有MV
  popularity: 100,                      // 热度
  publishTime: 747504000000,            // 发布时间
  liked: false,                         // 是否喜欢
  raw: { ... }                          // 原始API数据
}
```

### 格式化后的URL数据结构

```javascript
{
  id: 347230,                           // 歌曲ID
  url: "https://...",                   // 播放URL
  br: 320000,                           // 码率
  size: 13042460,                       // 文件大小（字节）
  md5: "abc123...",                     // 文件MD5
  code: 200,                            // 状态码
  expi: 1200,                           // 过期时间
  type: "mp3",                          // 文件类型
  gain: 0,                              // 增益
  peak: 0,                              // 峰值
  fee: 1,                               // 收费类型
  uf: null,                             // 未知字段
  payed: 0,                             // 是否已付费
  flag: 4,                              // 标志位
  canExtend: false,                     // 是否可延长
  freeTrialInfo: null,                  // 免费试听信息
  level: "exhigh",                      // 音质等级
  encodeType: "mp3",                    // 编码类型

  // 试听片段信息
  freeTimeTrialPrivilege: {
    resConsumable: false,               // 资源可消费
    userConsumable: false,              // 用户可消费
    type: 0,                            // 类型
    remainTime: 0                       // 剩余时间（秒）
  },

  // 可用性判断
  available: true,                      // 是否可用
  raw: { ... }                          // 原始API数据
}
```

### 可用性检查结果结构

```javascript
{
  id: 347230,                           // 歌曲ID
  available: true,                      // 是否可用
  message: "ok",                        // 消息
  br: 999000                            // 检查的码率
}
```

## 收费类型说明

```javascript
const feeTypes = {
  0: '免费或无版权',
  1: 'VIP歌曲',
  4: '购买专辑',
  8: '非会员可免费播放低音质，会员可播放高音质及下载'
}
```

## 辅助方法

### `formatSongData(song, privileges)`
格式化原始歌曲数据为统一格式。

### `formatDuration(duration)`
将毫秒时长格式化为 `mm:ss` 格式。

## 测试页面

### 歌曲详情测试
访问 `/test/song-detail` 可以测试歌曲详情API功能。

### 音乐URL测试
访问 `/test/song-url` 可以测试音乐URL获取和可用性检查功能。

### 测试步骤
1. **歌曲详情测试**：
   - 访问：`http://localhost:5173/test/song-detail`
   - 输入歌曲ID（例如：347230）
   - 点击"获取歌曲详情"按钮
   - 查看返回的格式化数据

2. **音乐URL测试**：
   - 访问：`http://localhost:5173/test/song-url`
   - 输入歌曲ID（例如：347230）
   - 选择API版本（推荐新版v1）
   - 选择音质等级
   - 点击"获取播放URL"或"检查可用性"
   - 可以直接试听音频

### 推荐测试ID
- `347230` - Beyond《海阔天空》
- `186016` - 陈奕迅《十年》
- `25906124` - 薛之谦《演员》
- `33894312` - 陈奕迅《浮夸》

## 错误处理

API调用可能出现的错误：
- 网络连接失败
- 歌曲ID不存在
- 服务器响应错误
- 数据格式异常

建议使用 try-catch 包装API调用：

```javascript
try {
  const song = await musicStore.fetchSongDetail(songId)
  // 处理成功结果
} catch (error) {
  console.error('获取歌曲详情失败:', error.message)
  // 处理错误情况
}
```

## 注意事项

1. **权限检查**：播放前请检查 `privilege.canPlay` 字段
2. **音质选择**：根据用户权限和网络状况选择合适的音质
3. **缓存策略**：考虑对频繁访问的歌曲进行本地缓存
4. **批量请求**：单次请求建议不超过50首歌曲
5. **错误重试**：网络错误时可以实现重试机制

## 集成到播放器

### 完整播放流程示例

```javascript
// 在播放器组件中使用
async function playSongById(songId) {
  try {
    // 1. 获取歌曲详情
    const song = await musicStore.fetchSongDetail(songId)

    // 2. 检查播放权限
    if (!song.privilege.canPlay) {
      message.warning('该歌曲暂无播放权限')
      return
    }

    // 3. 获取播放URL
    const urlData = await musicStore.fetchSongUrlV1(songId, 'exhigh')

    // 4. 检查URL可用性
    if (!urlData.available || !urlData.url) {
      message.warning('该歌曲暂无可用播放链接')
      return
    }

    // 5. 合并歌曲信息和URL
    const playableSong = {
      ...song,
      audioUrl: urlData.url,
      bitrate: urlData.br,
      fileSize: urlData.size
    }

    // 6. 开始播放
    musicStore.playSong(playableSong)
    message.success(`开始播放：${song.title}`)

  } catch (error) {
    console.error('播放失败:', error)
    message.error('获取歌曲信息失败')
  }
}

// 批量预加载播放列表
async function preloadPlaylist(songIds) {
  try {
    // 批量获取歌曲详情
    const songs = await musicStore.fetchSongDetail(songIds)

    // 批量获取播放URL
    const urlsData = await musicStore.fetchSongUrlV1(songIds, 'exhigh')

    // 合并数据
    const playableSongs = songs.map((song, index) => ({
      ...song,
      audioUrl: urlsData[index]?.url || '',
      available: urlsData[index]?.available || false
    }))

    return playableSongs.filter(song => song.available)
  } catch (error) {
    console.error('预加载播放列表失败:', error)
    return []
  }
}
```

## 最佳实践

### 1. 错误处理
- 始终使用 try-catch 包装API调用
- 检查返回数据的可用性
- 提供用户友好的错误提示

### 2. 性能优化
- 批量获取多首歌曲信息
- 缓存频繁访问的歌曲数据
- 预加载播放列表

### 3. 用户体验
- 显示加载状态
- 提供音质选择选项
- 处理网络超时情况

### 4. 安全考虑
- 检查播放权限
- 处理地区限制
- 验证URL有效性
