<template>
  <!-- 隐藏的音频播放器 -->
  <audio
    ref="audioElement"
    preload="metadata"
    @loadstart="onLoadStart"
    @loadedmetadata="onLoadedMetadata"
    @canplay="onCanPlay"
    @play="onPlay"
    @pause="onPause"
    @ended="onEnded"
    @timeupdate="onTimeUpdate"
    @error="onError"
    @volumechange="onVolumeChange"
    style="display: none;"
  />
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { useMusicStore } from '../stores/music'

// Store
const musicStore = useMusicStore()

// 音频元素引用
const audioElement = ref(null)

// 播放状态
const currentTime = ref(0)
const duration = ref(0)
const buffered = ref(0)

// 发出事件
const emit = defineEmits([
  'timeupdate',
  'durationchange',
  'play',
  'pause',
  'ended',
  'error',
  'loadstart',
  'canplay'
])

// 监听当前歌曲变化
watch(() => musicStore.currentSong, (newSong) => {
  if (newSong && newSong.audioUrl) {
    loadSong(newSong)
  }
}, { immediate: true })

// 监听播放状态变化
watch(() => musicStore.isPlaying, (isPlaying) => {
  if (!audioElement.value) return
  
  if (isPlaying) {
    audioElement.value.play().catch(err => {
      console.error('播放失败:', err)
      musicStore.isPlaying = false
      message.error('播放失败')
    })
  } else {
    audioElement.value.pause()
  }
})

// 监听音量变化
watch(() => musicStore.volume, (volume) => {
  if (audioElement.value) {
    audioElement.value.volume = volume / 100
  }
})

// 组件挂载时设置音频元素引用
onMounted(() => {
  if (audioElement.value) {
    musicStore.setAudioElement(audioElement.value)
    
    // 设置初始音量
    audioElement.value.volume = musicStore.volume / 100
  }
})

// 组件卸载时清理
onUnmounted(() => {
  if (audioElement.value) {
    audioElement.value.pause()
    audioElement.value.src = ''
  }
})

// 方法
function loadSong(song) {
  if (!audioElement.value || !song.audioUrl) return
  
  console.log('加载歌曲:', song.title, song.audioUrl)
  
  // 设置音频源
  audioElement.value.src = song.audioUrl
  audioElement.value.load()
}

// 跳转到指定时间
function seekTo(time) {
  if (audioElement.value && duration.value > 0) {
    audioElement.value.currentTime = time
  }
}

// 设置播放速度
function setPlaybackRate(rate) {
  if (audioElement.value) {
    audioElement.value.playbackRate = rate
  }
}

// 音频事件处理
function onLoadStart() {
  console.log('开始加载音频')
  emit('loadstart')
}

function onLoadedMetadata() {
  duration.value = audioElement.value.duration || 0
  emit('durationchange', duration.value)
  console.log('音频元数据加载完成，时长:', duration.value)
}

function onCanPlay() {
  console.log('音频可以播放')
  emit('canplay')
  
  // 如果应该播放，则开始播放
  if (musicStore.isPlaying) {
    audioElement.value.play().catch(err => {
      console.error('自动播放失败:', err)
    })
  }
}

function onPlay() {
  console.log('音频开始播放')
  musicStore.isPlaying = true
  emit('play')
}

function onPause() {
  console.log('音频暂停')
  musicStore.isPlaying = false
  emit('pause')
}

function onEnded() {
  console.log('音频播放结束')
  emit('ended')
  
  // 自动播放下一首
  musicStore.nextSong().catch(err => {
    console.error('播放下一首失败:', err)
  })
}

function onTimeUpdate() {
  if (audioElement.value) {
    currentTime.value = audioElement.value.currentTime

    // 计算缓冲进度
    if (audioElement.value.buffered.length > 0) {
      buffered.value = audioElement.value.buffered.end(audioElement.value.buffered.length - 1)
    }

    const timeData = {
      currentTime: currentTime.value,
      duration: duration.value,
      progress: duration.value > 0 ? (currentTime.value / duration.value) * 100 : 0
    }

    emit('timeupdate', timeData)

    // 发出全局事件供其他组件监听
    window.dispatchEvent(new CustomEvent('audio-timeupdate', { detail: timeData }))
  }
}

function onError(event) {
  const error = audioElement.value?.error
  let errorMessage = '播放出错'
  
  if (error) {
    switch (error.code) {
      case error.MEDIA_ERR_ABORTED:
        errorMessage = '播放被中止'
        break
      case error.MEDIA_ERR_NETWORK:
        errorMessage = '网络错误'
        break
      case error.MEDIA_ERR_DECODE:
        errorMessage = '解码错误'
        break
      case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
        errorMessage = '音频格式不支持'
        break
      default:
        errorMessage = '未知播放错误'
    }
  }
  
  console.error('音频播放错误:', errorMessage, event)
  message.error(errorMessage)
  
  musicStore.isPlaying = false
  emit('error', { message: errorMessage, event })
}

function onVolumeChange() {
  if (audioElement.value) {
    const volume = Math.round(audioElement.value.volume * 100)
    if (volume !== musicStore.volume) {
      musicStore.setVolume(volume)
    }
  }
}

// 暴露方法给父组件
defineExpose({
  seekTo,
  setPlaybackRate,
  currentTime,
  duration,
  buffered
})
</script>

<style scoped>
/* 隐藏的音频播放器不需要样式 */
</style>
