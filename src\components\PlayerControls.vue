<template>
  <!-- 播放控制栏 -->
  <div v-if="showPlayer" class="player-controls">
    <div class="left-section">
      <div class="time-display">{{ formatTime(currentTime) }}</div>
    </div>

    <div class="progress-slider-container">
      <!-- 进度条 -->
      <a-slider
        class="progress-slider"
        :value="progress"
        @change="setProgress"
        :tooltip-visible="false"
        :step="0.1"
        :min="0"
        :max="100"
      />
    </div>

    <div class="right-section">
      <div class="time-display">{{ formatTime(duration) }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useMusicStore } from '../stores/music';

// --------------------播放控制功能--------------------
const musicStore = useMusicStore();
const progress = ref(0); // 播放进度百分比
const currentTime = ref(0); // 当前播放时间（秒）
const duration = ref(0); // 总时长（秒）

// 播放状态控制
const showPlayer = computed(() => {
  return !!musicStore.currentSong; // 只要有当前歌曲就显示播放器
});

// 监听音频播放器的时间更新事件
function handleTimeUpdate(event) {
  const timeData = event.detail;
  currentTime.value = timeData.currentTime;
  duration.value = timeData.duration;
  progress.value = timeData.progress;
}

// 设置播放进度
function setProgress(newProgress) {
  // 立即更新进度值
  progress.value = newProgress;

  // 计算新的播放时间
  const newTime = (duration.value * newProgress) / 100;
  currentTime.value = newTime;

  // 通过音频播放器跳转到指定位置
  const audioPlayer = document.querySelector('audio');
  if (audioPlayer && duration.value > 0) {
    audioPlayer.currentTime = newTime;
  }

  console.log(`跳转到进度: ${newProgress}%, 时间: ${formatTime(newTime)}`);
}

// 组件挂载时监听全局事件
onMounted(() => {
  // 监听音频播放器的时间更新事件
  window.addEventListener('audio-timeupdate', handleTimeUpdate);
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('audio-timeupdate', handleTimeUpdate);
});



// 格式化时间为 MM:SS 格式
function formatTime(seconds) {
  if (!seconds && seconds !== 0) return '00:00';

  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}
</script>

<style lang="scss" scoped>
@use '../styles/design-system.scss' as *;



.player-controls {
  height: var(--player-controls-height);
  background: linear-gradient(135deg,
    rgba(20, 20, 30, 0.95),
    rgba(30, 30, 40, 0.95)
  );
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 -4px 20px rgba(0, 0, 0, 0.3),
    0 -2px 10px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  padding: 0 $spacing-xl;
  gap: $spacing-lg;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-sizing: border-box;

  // 添加微妙的发光效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
      transparent,
      rgba(24, 144, 255, 0.3),
      transparent
    );
  }

  .left-section {
    width: 70px;
    display: flex;
    justify-content: flex-start;
  }

  .right-section {
    width: 70px;
    display: flex;
    justify-content: flex-end;
  }

  .time-display {
    color: var(--text-secondary);
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    opacity: 0.8;
    @include transition(color);

    &:hover {
      color: var(--text-primary);
      opacity: 1;
    }
  }

  .progress-slider-container {
    flex: 1;
    max-width: calc(100% - 300px);
    margin: 0 $spacing-md;
    display: flex;
    align-items: center;
    justify-content: center;

  .progress-slider {
    margin: 0;
    width: 100%; // 增加宽度以填充容器
    max-width: 800px; // 限制最大宽度，保持居中效果
    padding: $spacing-sm 0; // 只保留垂直内边距，避免水平padding影响点击区域

    :deep(.ant-slider) {
      padding: $spacing-md 0; // 增加垂直内边距，扩大可点击区域
      margin: 0;
      width: 100%; // 确保滑块占满整个宽度
    }

      :deep(.ant-slider-rail) {
        background: linear-gradient(90deg,
          rgba(255, 255, 255, 0.08),
          rgba(255, 255, 255, 0.15),
          rgba(255, 255, 255, 0.08)
        );
        height: 8px; // 增加轨道高度，提升可点击区域
        border-radius: 4px;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
        cursor: pointer; // 添加指针样式
      }

      :deep(.ant-slider-track) {
        background: linear-gradient(90deg, #ec4141, #ff6b6b);
        height: 8px; // 增加进度条高度，与轨道保持一致
        border-radius: 4px;
        box-shadow:
          0 0 12px rgba(236, 65, 65, 0.5),
          0 3px 6px rgba(236, 65, 65, 0.3);
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(90deg,
            rgba(255, 255, 255, 0.3),
            transparent,
            rgba(255, 255, 255, 0.15)
          );
          border-radius: 4px;
        }
      }

      :deep(.ant-slider-handle) {
        display: none !important; // 完全隐藏手柄
      }

      // 禁用滑块位置变化的动画
      :deep(.ant-slider-track) {
        transition-property: transform, box-shadow !important;
        transition-duration: 0.2s !important;
      }

      // 点击时立即跳转，不使用动画
      &:active :deep(.ant-slider-track) {
        transition: none !important;
      }

      &:hover {
        :deep(.ant-slider-track) {
          box-shadow:
            0 0 16px rgba(236, 65, 65, 0.7),
            0 4px 10px rgba(236, 65, 65, 0.4);

          &::after {
            background: linear-gradient(90deg,
              rgba(255, 255, 255, 0.4),
              transparent,
              rgba(255, 255, 255, 0.2)
            );
          }
        }

        :deep(.ant-slider-rail) {
          background: linear-gradient(90deg,
            rgba(255, 255, 255, 0.12),
            rgba(255, 255, 255, 0.2),
            rgba(255, 255, 255, 0.12)
          );
        }
      }
    }
  }

// 响应式设计
@media (max-width: 768px) {
  .player-controls {
    padding: 0 $spacing-md;
  }
}

@media (max-width: 480px) {
  .player-controls {
    padding: 0 $spacing-sm;
    gap: $spacing-md;
  }
}
}
</style>