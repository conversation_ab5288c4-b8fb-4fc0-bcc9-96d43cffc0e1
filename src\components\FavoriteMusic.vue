<template>
  <MusicListView
    title="我喜欢的音乐"
    :songs="favoriteList"
    :current-user="currentUser"
    :play-count="totalPlayCount"
    :collect-count="totalCollectCount"
    @play-song="handlePlaySong"
    @toggle-like="handleToggleLike"
  />
</template>

<script setup>
import { ref, computed } from 'vue';
import { useMusicStore } from '../stores/music';
import MusicListView from './common/MusicListView.vue';

const musicStore = useMusicStore();

// 接收父组件传递的用户信息
const props = defineProps({
  currentUser: {
    type: Object,
    default: null
  }
});

// 我喜欢的音乐列表
const favoriteList = ref([
  {
    id: 1,
    title: 'Heartbeat',
    artist: 'Childish Gambino',
    album: 'Camp (Deluxe Edition)',
    duration: '04:25',
    liked: true,
    tags: []
  },
  {
    id: 2,
    title: 'Hurried',
    artist: 'Rain<PERSON>',
    album: 'Hurried',
    duration: '01:52',
    liked: true,
    tags: []
  },
  {
    id: 3,
    title: 'My Type',
    artist: 'The Chainsmokers / Emily Warren',
    album: 'Memories...Do Not Open (记忆·开启)',
    duration: '03:37',
    liked: true,
    tags: []
  },
  {
    id: 4,
    title: 'phone kisses',
    artist: 'lullaboy',
    album: 'phone kisses',
    duration: '01:54',
    liked: true,
    tags: []
  },
  {
    id: 5,
    title: "IT'S OKAY NOW",
    artist: 'TheReal FatBoi / TheReal Lil Kush',
    album: "IT'S OKAY NOW",
    duration: '02:51',
    liked: true,
    tags: []
  },
  {
    id: 6,
    title: 'Final wish (决定的瞬间)',
    artist: 'oxygen',
    album: 'Friends',
    duration: '02:08',
    liked: true,
    tags: []
  },
  {
    id: 7,
    title: 'Sacred Play Secret Place (神圣的游戏秘密的场所)',
    artist: 'Matryoshka',
    album: 'Labelnoirette',
    duration: '05:17',
    liked: true,
    tags: []
  },
  {
    id: 8,
    title: '陌生的朋友 (与陌生朋友的对话)',
    artist: 'Latheism',
    album: 'midnight12.5',
    duration: '03:13',
    liked: true,
    tags: []
  },
  {
    id: 9,
    title: '5:20AM',
    artist: '刀郎',
    album: '5:20AM',
    duration: '07:41',
    liked: true,
    tags: []
  },
  {
    id: 10,
    title: '奇妙白云 (奇妙朋友们了解彼此的过程)',
    artist: '奇妙白云',
    album: '奇妙白云',
    duration: '04:01',
    liked: true,
    tags: []
  },
  {
    id: 11,
    title: 'Baby',
    artist: 'Justin Bieber',
    album: '爱意与白月',
    duration: '03:36',
    liked: true,
    tags: []
  }
]);

// 计算属性
const totalPlayCount = computed(() => '10w+');
const totalCollectCount = computed(() => '2858');

// 事件处理
function handlePlaySong(song) {
  musicStore.currentSong = song;
}

function handleToggleLike(song) {
  musicStore.toggleLike?.(song.id);
}
</script>