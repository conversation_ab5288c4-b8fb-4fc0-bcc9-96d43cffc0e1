<template>
    <div class="new-song-mode">
        <!-- 页面头部 -->
        <div class="mode-header">
            <div class="header-content">
                <h2>听歌模式</h2>
                <p class="sub-title">发现最新最热的音乐作品</p>

                <!-- 刷新提示 -->
                <div v-if="showRefreshTip" class="refresh-tip">
                    <div class="tip-content">
                        <span class="tip-icon">💡</span>
                        <span class="tip-text">数据可能不是最新的，</span>
                        <button class="refresh-link" @click="forceRefresh">点击刷新</button>
                        <span class="tip-text">获取最新数据</span>
                    </div>
                </div>
            </div>

            <!-- 头部控制按钮 -->
            <div class="header-controls">
                <div class="category-selector">
                    <div class="current-category" @click="toggleCategoryGrid">
                        {{ getCurrentCategoryLabel() }}
                        <down-outlined :class="{ 'rotated': showCategoryGrid }" />
                    </div>

                    <div v-if="showCategoryGrid" class="category-grid">
                        <div
                            v-for="category in categoryOptions"
                            :key="category.value"
                            class="category-item"
                            :class="{ active: selectedCategory === category.value }"
                            @click="selectCategory(category.value)"
                        >
                            {{ category.label }}
                        </div>
                    </div>
                </div>

                <UiButton
                    :loading="playlistLoading"
                    @click="refreshCollections"
                    size="small"
                    variant="outlined"
                >
                    <reload-outlined />
                    刷新
                </UiButton>
            </div>
        </div>

        <!-- 听歌模式分类 -->
        <div class="mode-categories">
            <div
                class="mode-category"
                v-for="(category, index) in categories"
                :key="index"
                @click="handleCategoryClick(category)"
            >
                <div class="category-icon">
                    <component :is="category.icon" />
                </div>
                <div class="category-name">{{ category.name }}</div>
            </div>
        </div>

        <div class="section-divider">
            <h3>精选歌单</h3>
            <p>探索更多听歌模式</p>
        </div>

        <!-- 加载状态 -->
        <div v-if="playlistLoading" class="loading-container">
            <div class="collections-skeleton">
                <div
                    v-for="i in 12"
                    :key="i"
                    class="skeleton-collection"
                >
                    <UiSkeleton
                        :avatar="false"
                        :title="false"
                        :paragraph="false"
                        class="skeleton-content"
                    >
                        <div class="skeleton-cover"></div>
                        <div class="skeleton-info">
                            <div class="skeleton-title"></div>
                            <div class="skeleton-description"></div>
                        </div>
                    </UiSkeleton>
                </div>
            </div>
        </div>

        <!-- 歌单网格 -->
        <div v-else class="song-collections">
            <PlaylistCard
                v-for="collection in filteredCollections"
                :key="collection.id"
                :playlist="collection"
                variant="compact"
                @click="openPlaylistDetail"
                @play="handlePlayCollection"
                class="collection-item"
            />
        </div>

        <!-- 空状态 -->
        <div v-if="!playlistLoading && filteredCollections.length === 0" class="empty-state">
            <div class="empty-icon">
                <inbox-outlined />
            </div>
            <p>暂无歌单</p>
            <UiButton type="primary" @click="refreshCollections">
                重新加载
            </UiButton>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import {
    FireOutlined,
    SoundOutlined,
    HeartOutlined,
    PlayCircleOutlined,
    ThunderboltOutlined,
    ReloadOutlined,
    InboxOutlined,
    DownOutlined
} from '@ant-design/icons-vue';
import UiButton from './ui/UiButton.vue';
import UiSkeleton from './ui/UiSkeleton.vue';
import PlaylistCard from './music/PlaylistCard.vue';
import { useCategoryStore } from '../stores/category';
import { usePlaylistStore } from '../stores/playlist';

// 定义事件
const emit = defineEmits(['change-view']);

// 路由
const router = useRouter();

// 分类store
const categoryStore = useCategoryStore();
const playlistStore = usePlaylistStore();

// 响应式状态
const selectedCategory = ref('all');
const showCategoryGrid = ref(false);

// 分类选项 - 从store获取
const categoryOptions = computed(() => {
    return categoryStore.formattedCategories;
});

// 歌单数据 - 从store获取
const collections = computed(() => {
    return playlistStore.playlists;
});

// 加载状态
const playlistLoading = computed(() => {
    return playlistStore.loading;
});

// 刷新提示状态
const showRefreshTip = computed(() => {
    return playlistStore.showRefreshTip;
});

// 定义听歌模式分类
const categories = ref([
    { name: '每日推荐', icon: FireOutlined },
    { name: '心动模式', icon: HeartOutlined },
    { name: '私人FM', icon: SoundOutlined },
    { name: '私人DJ', icon: PlayCircleOutlined },
    { name: '相似音乐', icon: ThunderboltOutlined }
]);




// 计算属性 - 直接使用store中的歌单数据（不需要过滤）
const filteredCollections = computed(() => {
    console.log('当前显示歌单数量:', collections.value.length);
    return collections.value;
});

// 方法
function handleCategoryClick(category) {
    console.log('点击分类:', category.name);

    // 除了"相似音乐"外，其他分类都需要登录
    if (category.name !== '相似音乐') {
        // 显示登录提示
        message.info({
            content: `${category.name}功能需要登录后使用，请先登录您的账号`,
            duration: 3
        });
        return;
    }

    // 相似音乐的处理逻辑
    console.log('进入相似音乐模式');
}

// 分类选择器相关方法
function toggleCategoryGrid() {
    showCategoryGrid.value = !showCategoryGrid.value;
}

async function selectCategory(value) {
    selectedCategory.value = value;
    showCategoryGrid.value = false;

    // 根据分类获取歌单数据
    const categoryName = value === 'all' ? '全部' : value;
    console.log('切换分类:', { value, categoryName });

    try {
        await playlistStore.fetchPlaylistsByCategory(categoryName);
        console.log('分类切换完成，当前歌单数量:', collections.value.length);
    } catch (error) {
        console.error('分类切换失败:', error);
    }
}

function getCurrentCategoryLabel() {
    const category = categoryOptions.value.find(cat => cat.value === selectedCategory.value);
    return category ? category.label : '🎵 全部';
}

// 点击外部关闭网格
function handleClickOutside(event) {
    const categorySelector = event.target.closest('.category-selector');
    if (!categorySelector && showCategoryGrid.value) {
        showCategoryGrid.value = false;
    }
}

async function refreshCollections() {
    try {
        const categoryName = selectedCategory.value === 'all' ? '全部' : selectedCategory.value;
        await playlistStore.fetchPlaylistsByCategory(categoryName);
        console.log('刷新歌单完成');
    } catch (error) {
        console.error('刷新失败:', error);
    }
}

// 强制刷新当前分类数据
async function forceRefresh() {
    try {
        await playlistStore.forceRefreshCurrentCategory();
        console.log('强制刷新完成');
    } catch (error) {
        console.error('强制刷新失败:', error);
    }
}

function handlePlayCollection(playlist) {
    console.log('播放歌单:', playlist.title);
    // 可以添加播放逻辑
}



// 打开歌单详情页
function openPlaylistDetail(playlist) {
    // 使用路由导航到歌单详情页
    router.push(`/playlist/${playlist.id}`);
}

// 组件挂载时的初始化
onMounted(async () => {
    console.log('NewSongMode 组件已挂载');

    // 初始化分类数据
    try {
        await categoryStore.initializeCategories();
        console.log('分类数据初始化完成，可用分类:', categoryOptions.value);
    } catch (error) {
        console.error('分类数据初始化失败:', error);
    }

    // 初始化缓存
    playlistStore.initializeCache();

    // 初始化歌单数据
    try {
        await playlistStore.fetchPlaylistsByCategory('全部');
        console.log('精品歌单初始化完成');
    } catch (error) {
        console.error('精品歌单初始化失败:', error);
    }

    // 添加点击外部关闭事件监听
    document.addEventListener('click', handleClickOutside);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});


</script>

<style lang="scss" scoped>
@import '../styles/design-system.scss';

.new-song-mode {
    padding: $spacing-md;
    color: var(--text-primary);
    height: 100%;
    overflow-y: scroll; // 强制显示滚动条，避免内容变化时的抖动
    overflow-x: hidden;
    @include custom-scrollbar(); // 使用自定义滚动条样式

    // 确保内容不会被播放控制栏遮挡
    padding-bottom: calc($spacing-lg + var(--player-controls-height));

    // 禁止文字选中和图片拖拽
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    // 禁止所有元素拖拽
    * {
        -webkit-user-drag: none;
        -khtml-user-drag: none;
        -moz-user-drag: none;
        -o-user-drag: none;
        pointer-events: auto; // 保持点击事件
    }

    // 特别针对图片禁止拖拽
    img {
        -webkit-user-drag: none;
        -khtml-user-drag: none;
        -moz-user-drag: none;
        -o-user-drag: none;
        pointer-events: none; // 图片完全禁止交互
    }

    // 页面头部
    .mode-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: $spacing-lg;
        padding-bottom: $spacing-md;
        border-bottom: 1px solid var(--border-color);

        .header-content {
            h2 {
                font-size: $font-size-xxl;
                font-weight: $font-weight-bold;
                color: var(--text-primary);
                margin: 0 0 $spacing-xs 0;
            }

            .sub-title {
                font-size: $font-size-md;
                color: var(--text-secondary);
                margin: 0;
            }

            // 刷新提示样式
            .refresh-tip {
                margin-top: $spacing-md;
                padding: $spacing-sm $spacing-md;
                background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 193, 7, 0.08));
                border: 1px solid rgba(255, 193, 7, 0.3);
                border-radius: $border-radius-lg;
                backdrop-filter: blur(10px);

                .tip-content {
                    display: flex;
                    align-items: center;
                    gap: $spacing-xs;
                    font-size: $font-size-sm;

                    .tip-icon {
                        font-size: 16px;
                    }

                    .tip-text {
                        color: var(--text-secondary);
                    }

                    .refresh-link {
                        background: none;
                        border: none;
                        color: var(--primary-color);
                        cursor: pointer;
                        font-size: $font-size-sm;
                        font-weight: $font-weight-medium;
                        text-decoration: underline;
                        padding: 0;
                        transition: color 0.3s ease;

                        &:hover {
                            color: #40a9ff;
                        }
                    }
                }
            }
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 10px;

            // 自定义分类选择器样式
            .category-selector {
                position: relative;

                .current-category {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    padding: 8px 16px;
                    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.06));
                    border: 1px solid rgba(255, 255, 255, 0.25);
                    border-radius: $border-radius-xl;
                    backdrop-filter: blur(20px);
                    color: var(--text-primary);
                    font-weight: $font-weight-medium;
                    font-size: $font-size-md;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    min-width: 140px;
                    justify-content: space-between;

                    &:hover {
                        border-color: rgba(24, 144, 255, 0.4);
                        background: linear-gradient(135deg, rgba(24, 144, 255, 0.18), rgba(24, 144, 255, 0.1));
                        transform: translateY(-1px);
                    }

                    .anticon {
                        transition: transform 0.3s ease;

                        &.rotated {
                            transform: rotate(180deg);
                        }
                    }
                }

                .category-grid {
                    position: absolute;
                    top: calc(100% + 8px);
                    right: 0; // 改为右对齐
                    z-index: 1000;
                    padding: 12px;
                    background: linear-gradient(135deg, rgba(20, 20, 30, 0.95), rgba(30, 30, 40, 0.95));
                    backdrop-filter: blur(20px);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 12px;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                    display: grid;
                    grid-template-columns: repeat(4, 1fr); // 改为4列
                    gap: 8px;
                    width: 440px; // 增加宽度以适应4列布局
                    max-width: 90vw;
                    max-height: 200px; // 设置最大高度为4行的高度 (4 * 48px + gaps)
                    overflow-y: auto; // 超出部分显示滚动条

                    // 自定义滚动条样式
                    &::-webkit-scrollbar {
                        width: 6px;
                    }

                    &::-webkit-scrollbar-track {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 3px;
                    }

                    &::-webkit-scrollbar-thumb {
                        background: linear-gradient(135deg, rgba(24, 144, 255, 0.6), rgba(24, 144, 255, 0.4));
                        border-radius: 3px;
                        transition: background 0.3s ease;

                        &:hover {
                            background: linear-gradient(135deg, rgba(24, 144, 255, 0.8), rgba(24, 144, 255, 0.6));
                        }
                    }

                    &::-webkit-scrollbar-corner {
                        background: transparent;
                    }

                    .category-item {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding: 12px 8px;
                        background: rgba(255, 255, 255, 0.05);
                        border: 1px solid rgba(255, 255, 255, 0.1);
                        border-radius: 8px;
                        color: var(--text-primary);
                        font-size: 13px;
                        font-weight: 500;
                        text-align: center;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        min-height: 40px;
                        white-space: nowrap;

                        &:hover {
                            background: rgba(24, 144, 255, 0.15);
                            border-color: rgba(24, 144, 255, 0.3);
                            transform: translateY(-2px);
                            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
                        }

                        &.active {
                            background: rgba(24, 144, 255, 0.2);
                            border-color: rgba(24, 144, 255, 0.4);
                            color: #fff;
                        }
                    }
                }
            }
        }
    }

    // 听歌模式分类
    .mode-categories {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: $spacing-sm;
        margin-bottom: $spacing-lg;
    }

    // 听歌模式分类
    .mode-categories {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: $spacing-sm;
        margin-bottom: $spacing-lg;

        .mode-category {
            @include card;
            padding: $spacing-lg $spacing-md;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            @include hover-lift;
            min-height: 100px;

            &:hover {
                border-color: var(--primary-color);
                background: rgba(24, 144, 255, 0.05);
            }

            .category-icon {
                font-size: 28px;
                margin-bottom: $spacing-sm;
                color: #1890ff; // 使用明亮的蓝色，确保在深色背景下可见
                @include transition();

                &:hover {
                    color: #40a9ff; // 悬停时使用更亮的蓝色
                }
            }

            .category-name {
                font-size: $font-size-sm;
                color: var(--text-primary);
                text-align: center;
                font-weight: $font-weight-medium;
            }
        }
    }

    // 分割线
    .section-divider {
        margin-bottom: $spacing-lg;

        h3 {
            font-size: $font-size-xl;
            font-weight: $font-weight-bold;
            color: var(--text-primary);
            margin: 0 0 $spacing-xs 0;
        }

        p {
            font-size: $font-size-md;
            color: var(--text-secondary);
            margin: 0;
        }
    }

    // 加载状态
    .loading-container {
        .collections-skeleton {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: $spacing-lg;

            .skeleton-collection {
                .skeleton-content {
                    .skeleton-cover {
                        @include loading-shimmer;
                        width: 100%;
                        height: 160px;
                        border-radius: $border-radius-lg $border-radius-lg 0 0;
                        margin-bottom: $spacing-sm;
                    }

                    .skeleton-info {
                        padding: $spacing-md;

                        .skeleton-title {
                            @include loading-shimmer;
                            height: 16px;
                            width: 80%;
                            border-radius: $border-radius-sm;
                            margin-bottom: $spacing-xs;
                        }

                        .skeleton-description {
                            @include loading-shimmer;
                            height: 14px;
                            width: 60%;
                            border-radius: $border-radius-sm;
                        }
                    }
                }
            }
        }
    }

    // 歌单网格 - 更紧凑的布局
    .song-collections {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: $spacing-sm;
        

        .collection-item {
            @include hover-lift;
        }
    }

    // 空状态
    .empty-state {
        text-align: center;
        padding: $spacing-xxl;
        color: var(--text-secondary);

        .empty-icon {
            font-size: 64px;
            color: var(--text-disabled);
            margin-bottom: $spacing-lg;
        }

        p {
            font-size: $font-size-xl;
            margin: 0 0 $spacing-lg 0;
        }
    }

    // 响应式设计
    @include respond-to(xs) {
        padding: $spacing-md;

        .mode-header {
            flex-direction: column;
            align-items: flex-start;
            gap: $spacing-md;

            .header-controls {
                width: 100%;
                justify-content: space-between;
            }
        }

        .mode-categories {
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: $spacing-sm;

            .mode-category {
                padding: $spacing-md $spacing-sm;
                min-height: 80px;

                .category-icon {
                    font-size: 24px;
                    color: #1890ff; // 确保响应式版本也使用明亮颜色

                    &:hover {
                        color: #40a9ff;
                    }
                }

                .category-name {
                    font-size: $font-size-xs;
                }
            }
        }

        .song-collections {
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            gap: $spacing-md;
        }

        .collections-skeleton {
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            gap: $spacing-md;
        }
    }
}
</style>