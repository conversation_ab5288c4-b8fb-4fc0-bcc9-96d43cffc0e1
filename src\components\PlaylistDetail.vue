<template>
  <div class="playlist-detail" ref="playlistDetailRef">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <loading-outlined class="loading-icon" />
      <p>正在加载歌单详情...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <p class="error-message">{{ error }}</p>
      <a-button @click="initializeData">重新加载</a-button>
    </div>

    <!-- 页面头部 -->
    <div v-else-if="playlist" class="page-header">
      <div class="playlist-info">
        <div class="playlist-cover">
          <img :src="playlist.coverUrl" :alt="playlist.name" />
          <div class="play-overlay">
            <play-circle-filled class="play-icon" @click="playAll" />
          </div>
        </div>
        <div class="playlist-details">
          <h1 class="playlist-title">{{ playlist.name }}</h1>
          <div class="playlist-meta">
            <a-avatar size="small" class="creator-avatar" :src="playlist.creator?.avatarUrl">
              <template #icon><user-outlined /></template>
            </a-avatar>
            <span class="creator-name">{{ playlist.creator?.nickname || 'Unknown' }}</span>
            <span class="create-time">{{ new Date(playlist.createTime).toLocaleDateString() }}</span>
            <span class="separator">•</span>
            <span class="song-count">{{ playlist.trackCount }}首歌</span>
            <span class="separator">•</span>
            <span class="play-count">播放 {{ totalPlayCount }}</span>
            <span class="separator">•</span>
            <span class="collect-count">收藏 {{ totalCollectCount }}</span>
          </div>
          <div class="playlist-actions">
            <a-button type="primary" class="play-all-btn" @click="playAll" :disabled="!tracks || tracks.length === 0">
              <play-circle-outlined />
              播放全部
            </a-button>
            <a-button class="action-btn" @click="toggleCollectPlaylist">
              <heart-filled v-if="isPlaylistCollected" style="color: #ff4d4f" />
              <heart-outlined v-else />
              {{ isPlaylistCollected ? '已收藏' : '收藏歌单' }}
            </a-button>
          </div>
        </div>

        <!-- 右侧描述区域 -->
        <div class="playlist-description-sidebar" v-if="playlist.description">
          <h4>歌单简介</h4>
          <div class="description-content">
            <p>{{ playlist.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 歌曲列表 -->
    <div v-if="playlist" class="song-list-container">
      <div class="list-header">
        <div class="header-tabs">
          <span class="tab active">歌曲</span>
        </div>
        <div class="search-box">
          <a-input
            placeholder="搜索歌曲、歌手、专辑"
            size="small"
            class="search-input"
            v-model:value="searchQuery"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
        </div>
      </div>

      <div class="song-list">

        <div class="song-table">
          <div class="table-header">
            <div class="col-index"></div>
            <div class="col-title">音乐标题</div>
            <div class="col-artist">歌手</div>
            <div class="col-album">专辑</div>
            <div class="col-duration">时长</div>
          </div>

          <div class="table-body">
            <!-- 歌曲加载状态 -->
            <div v-if="tracksLoading" class="loading-songs">
              <loading-outlined class="loading-icon" />
              <span>正在加载歌曲...</span>
            </div>

            <!-- 歌曲列表 -->
            <div v-else-if="filteredSongs && filteredSongs.length > 0">
              <div v-for="(song, index) in filteredSongs" :key="song.id" class="song-row"
                :class="{
                  active: currentPlayingSong && currentPlayingSong.id === song.id,
                  playing: isPlaying && currentPlayingSong && currentPlayingSong.id === song.id
                }"
                @dblclick="playSong(song)">
                <div class="col-index">
                  <span class="song-number" v-if="!currentPlayingSong || currentPlayingSong.id !== song.id">
                    {{ String(index + 1).padStart(2, '0') }}
                  </span>
                  <play-circle-outlined v-else-if="!isPlaying" class="play-btn" @click="resumePlay" />
                  <pause-circle-outlined v-else class="pause-btn" @click="pausePlay" />
                </div>
                <div class="col-title">
                  <div class="song-info">
                    <span class="song-name">{{ song.name }}</span>
                    <div class="song-tags" v-if="song.alias && song.alias.length > 0">
                      <span v-for="alias in song.alias" :key="alias" class="tag">{{ alias }}</span>
                    </div>
                  </div>
                </div>
                <div class="col-artist">
                  <span class="artist-name">{{ song.artist }}</span>
                </div>
                <div class="col-album">
                  <span class="album-name">{{ song.album }}</span>
                </div>
                <div class="col-duration">
                  <div class="duration-actions">
                    <heart-filled v-if="musicStore.isLiked(song.id)" class="like-btn liked" @click="toggleLike(song)" />
                    <heart-outlined v-else class="like-btn" @click="toggleLike(song)" />
                    <share-alt-outlined class="action-icon" />
                    <more-outlined class="action-icon" />
                    <span class="duration">{{ song.durationFormatted }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="empty-songs">
              <p>暂无歌曲</p>
            </div>

            <!-- 加载更多提示 -->
            <div v-if="filteredSongs.length > 0" class="load-more-section">
              <!-- 正在加载更多 -->
              <div v-if="isLoadingMore" class="loading-more">
                <loading-outlined class="loading-icon" />
                <span>正在加载更多歌曲...</span>
              </div>

              <!-- 还有更多数据 -->
              <div v-else-if="playlistDetailStore.hasMoreTracks" class="has-more">
                <span>滚动到底部加载更多歌曲</span>
              </div>

              <!-- 没有更多数据 -->
              <div v-else class="no-more">
                <span>已加载全部 {{ tracks.length }} 首歌曲</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { useMusicStore } from '../stores/music';
import { usePlaylistDetailStore } from '../stores/playlistDetail';
import { message } from 'ant-design-vue';
import {
  PlayCircleFilled,
  PlayCircleOutlined,
  PauseCircleOutlined,
  UserOutlined,
  MoreOutlined,
  SearchOutlined,
  HeartFilled,
  HeartOutlined,
  ShareAltOutlined,
  LoadingOutlined
} from '@ant-design/icons-vue';

const route = useRoute();
const musicStore = useMusicStore();
const playlistDetailStore = usePlaylistDetailStore();

// 响应式状态
const searchQuery = ref('');
const isPlaylistCollected = ref(false);

// 计算属性
const playlist = computed(() => playlistDetailStore.formattedPlaylist);
const tracks = computed(() => playlistDetailStore.formattedTracks);
const loading = computed(() => playlistDetailStore.loading);
const tracksLoading = computed(() => playlistDetailStore.tracksLoading);
const error = computed(() => playlistDetailStore.error);

// 当前播放状态
const currentPlayingSong = computed(() => musicStore.currentSong);
const isPlaying = computed(() => musicStore.isPlaying);

// 滚动加载相关
const playlistDetailRef = ref(null);
const isLoadingMore = ref(false);
const scrollTimer = ref(null);

// 过滤后的歌曲列表
const filteredSongs = computed(() => {
  if (!searchQuery.value || !tracks.value) {
    return tracks.value || [];
  }

  const keyword = searchQuery.value.toLowerCase();
  return tracks.value.filter(song => {
    return song.name.toLowerCase().includes(keyword) ||
           song.artist.toLowerCase().includes(keyword) ||
           song.album.toLowerCase().includes(keyword);
  });
});

// 格式化的歌单统计信息
const totalPlayCount = computed(() => {
  if (!playlist.value) return '0';
  const count = playlist.value.playCount;
  if (count > 100000000) return `${Math.floor(count / 100000000)}亿`;
  if (count > 10000) return `${Math.floor(count / 10000)}万`;
  return count.toString();
});

const totalCollectCount = computed(() => {
  if (!playlist.value) return '0';
  const count = playlist.value.subscribedCount;
  if (count > 10000) return `${Math.floor(count / 10000)}万`;
  return count.toString();
});

// 初始化数据加载
async function initializeData() {
  const playlistId = route.params.id;
  console.log('initializeData 被调用，歌单ID:', playlistId);
  if (!playlistId) {
    message.error('歌单ID不存在');
    return;
  }

  try {
    console.log('开始初始化歌单详情...');
    await playlistDetailStore.initializePlaylistDetail(playlistId);
    console.log('歌单详情初始化完成');
  } catch (err) {
    console.error('加载歌单详情失败:', err);
    message.error('加载歌单详情失败，请稍后重试');
  }
}

// 播放所有歌曲
async function playAll() {
  if (tracks.value && tracks.value.length > 0) {
    try {
      // 格式化歌曲列表为播放器需要的格式
      const formattedTracks = tracks.value.map(track => ({
        id: track.id,
        title: track.name,
        artist: track.ar?.map(artist => artist.name).join(' / ') || '未知歌手',
        album: track.al?.name || '未知专辑',
        coverUrl: track.al?.picUrl || '',
        duration: track.dt || 0,
        durationText: musicStore.formatDuration(track.dt),
        raw: track
      }))

      // 播放整个歌单
      await musicStore.playPlaylist(formattedTracks, 0)
      message.success(`开始播放歌单：${playlist.value?.name}`)
    } catch (error) {
      console.error('播放歌单失败:', error)
      message.error(error.message || '播放失败')
    }
  }
}

// 播放指定歌曲
async function playSong(song) {
  try {
    // 获取当前歌单的所有歌曲作为播放列表
    const formattedTracks = tracks.value.map(track => ({
      id: track.id,
      title: track.name,
      artist: track.ar?.map(artist => artist.name).join(' / ') || '未知歌手',
      album: track.al?.name || '未知专辑',
      coverUrl: track.al?.picUrl || '',
      duration: track.dt || 0,
      durationText: musicStore.formatDuration(track.dt),
      raw: track
    }))

    // 播放指定歌曲，并设置播放列表
    await musicStore.playSong(song.id, formattedTracks)
    message.success(`开始播放：${song.name}`)
  } catch (error) {
    console.error('播放歌曲失败:', error)
    message.error(error.message || '播放失败')
  }
}

// 暂停播放
function pausePlay() {
  musicStore.togglePlay();
}

// 恢复播放
function resumePlay() {
  musicStore.togglePlay();
}

// 切换喜欢状态
function toggleLike(song) {
  musicStore.toggleLike(song.id);
}

// 切换歌单收藏状态
function toggleCollectPlaylist() {
  isPlaylistCollected.value = !isPlaylistCollected.value;

  if (isPlaylistCollected.value) {
    message.success(`已收藏歌单: ${playlist.value?.name}`);
  } else {
    message.success(`已取消收藏歌单: ${playlist.value?.name}`);
  }
}

// 滚动加载更多
async function handleLoadMore() {
  if (isLoadingMore.value || !playlistDetailStore.hasMoreTracks) {
    return;
  }

  // 保存当前滚动位置
  const currentScrollTop = playlistDetailRef.value?.scrollTop || 0;

  try {
    isLoadingMore.value = true;
    console.log('开始加载更多歌曲...当前歌曲数量:', tracks.value?.length || 0);
    await playlistDetailStore.loadMoreTracks();
    console.log('加载更多歌曲完成，新的歌曲数量:', tracks.value?.length || 0);

    // 等待DOM更新后恢复滚动位置
    await nextTick();
    if (playlistDetailRef.value) {
      playlistDetailRef.value.scrollTop = currentScrollTop;
    }
  } catch (error) {
    console.error('加载更多歌曲失败:', error);
    message.error('加载更多歌曲失败');
  } finally {
    isLoadingMore.value = false;
  }
}

// 滚动事件处理（带防抖）
function handleScroll(event) {
  // 清除之前的定时器
  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value);
  }

  // 设置防抖延迟
  scrollTimer.value = setTimeout(() => {
    const element = event.target;
    const scrollTop = element.scrollTop;
    const scrollHeight = element.scrollHeight;
    const clientHeight = element.clientHeight;

    // 当滚动到距离底部100px时触发加载更多
    const threshold = 100;
    if (scrollHeight - scrollTop - clientHeight < threshold) {
      handleLoadMore();
    }
  }, 100); // 100ms防抖延迟
}

// 监听路由变化，重新加载数据
watch(() => route.params.id, (newId, oldId) => {
  console.log('路由ID变化:', { newId, oldId });
  if (newId && newId !== oldId) {
    // 只有当歌单ID真正改变时才重新初始化
    console.log('歌单ID改变，清空数据并重新初始化');
    playlistDetailStore.clearPlaylistDetail();
    initializeData();
  } else {
    console.log('歌单ID未改变，跳过重新初始化');
  }
});

// 组件挂载时初始化数据
onMounted(() => {
  initializeData();

  // 添加滚动监听
  nextTick(() => {
    if (playlistDetailRef.value) {
      playlistDetailRef.value.addEventListener('scroll', handleScroll);
    }
  });
});

// 组件卸载时移除滚动监听
onUnmounted(() => {
  if (playlistDetailRef.value) {
    playlistDetailRef.value.removeEventListener('scroll', handleScroll);
  }

  // 清理定时器
  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value);
  }
});
</script>

<style lang="scss" scoped>
@import '../styles/design-system.scss';

.playlist-detail {
  height: 100%;
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  color: var(--text-primary);
  overflow-y: scroll; // 强制显示滚动条，避免内容变化时的抖动
  overflow-x: hidden;
  @include custom-scrollbar(); // 使用自定义滚动条样式

  // 确保内容不会被播放控制栏遮挡
  padding-bottom: var(--player-controls-height);

  // 加载状态
  .loading-container,
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 50vh;
    gap: $spacing-md;

    .loading-icon {
      font-size: 32px;
      color: var(--primary-color);
      animation: spin 1s linear infinite;
    }

    .error-message {
      color: var(--text-secondary);
      font-size: $font-size-lg;
    }
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .page-header {
    padding: 30px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(10px);

    .playlist-info {
      display: flex;
      gap: 24px;
      align-items: flex-start;

      .playlist-cover {
        position: relative;
        width: 200px;
        height: 200px;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .play-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s;

          .play-icon {
            font-size: 60px;
            color: var(--text-primary);
            cursor: pointer;
          }
        }

        &:hover .play-overlay {
          opacity: 1;
        }
      }

      .playlist-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .playlist-title {
          font-size: 32px;
          font-weight: bold;
          margin-bottom: 16px;
        }

        .playlist-meta {
          display: flex;
          align-items: center;
          gap: $spacing-sm;
          margin-bottom: $spacing-lg;
          font-size: $font-size-md;
          flex-wrap: wrap;

          .creator-avatar {
            background-color: var(--bg-card);
          }

          .creator-name {
            color: var(--primary-color);
            cursor: pointer;
            font-weight: $font-weight-medium;
            @include transition(color);

            &:hover {
              color: var(--primary-hover);
            }
          }

          .create-time {
            color: var(--text-secondary);
            opacity: 0.8;
          }

          .separator {
            color: var(--text-secondary);
            opacity: 0.5;
            margin: 0 6px;
          }

          .song-count {
            color: var(--text-primary);
            font-weight: $font-weight-medium;
            opacity: 0.9;
          }

          .play-count,
          .collect-count {
            color: var(--primary-color);
            font-weight: $font-weight-medium;
            opacity: 1;
          }
        }

        .playlist-actions {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: 16px;

          .play-all-btn {
            background: #ec4141;
            border: none;
            border-radius: 24px;
            padding: 12px 32px;
            font-size: 16px;
            font-weight: 500;
            height: 48px;
            min-width: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            &:hover {
              background: #d73027;
              transform: translateY(-1px);
            }
          }

          .action-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            border-radius: 24px;
            padding: 12px 20px;
            font-size: 14px;
            height: 48px;
            min-width: 48px;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
              background: rgba(255, 255, 255, 0.2);
              border-color: var(--border-hover);
              transform: translateY(-1px);
            }
          }
        }
      }

      // 右侧描述区域
      .playlist-description-sidebar {
        flex: 0 0 300px; // 固定宽度300px
        margin-left: auto;
        padding: $spacing-lg;
        background: rgba(255, 255, 255, 0.05);
        border-radius: $border-radius-lg;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);

        h4 {
          font-size: $font-size-lg;
          font-weight: $font-weight-bold;
          color: var(--text-primary);
          margin: 0 0 $spacing-md 0;
          padding-bottom: $spacing-sm;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .description-content {
          max-height: 120px;
          overflow-y: auto;
          @include custom-scrollbar();

          p {
            color: var(--text-secondary);
            line-height: 1.6;
            margin: 0;
            font-size: $font-size-sm;
            white-space: pre-line; // 保持换行格式
          }
        }
      }
    }
  }

  .song-list-container {
    padding: 0 30px;

    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--border-color);
      padding: $spacing-md;
      position: relative;

      .header-tabs {
        display: flex;
        gap: $spacing-xxl;

        .tab {
          font-size: $font-size-lg;
          cursor: pointer;
          padding-bottom: $spacing-sm;
          opacity: 0.6;
          @include transition(opacity);

          &.active {
            opacity: 1;
            border-bottom: 2px solid #ec4141;
          }

          &:hover {
            opacity: 0.8;
          }
        }
      }

      .search-box {
        .search-input {
          width: 240px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid var(--border-color);
          border-radius: $spacing-md;

          :deep(.ant-input) {
            background: transparent;
            color: var(--text-primary);
            border: none;
            padding: 8px 12px;

            &::placeholder {
              color: var(--text-secondary);
            }
          }

          :deep(.ant-input-prefix) {
            color: var(--text-secondary);
            margin-right: 8px;
          }
        }
      }
    }

    .song-list {

      .song-table {
        .table-header {
          display: flex;
          padding: 12px 0;
          border-bottom: 1px solid var(--border-color);
          font-size: 14px;
          opacity: 0.6;
          font-weight: 500;

          .col-index {
            width: 60px;
            text-align: center;
          }

          .col-title {
            flex: 2;
            padding-left: 16px;
          }

          .col-artist {
            flex: 1;
          }

          .col-album {
            flex: 1;
          }

          .col-duration {
            width: 200px;
            text-align: right;
            padding-right: 16px;
          }
        }

        .table-body {
          padding-bottom: $spacing-xl; // 添加底部边距，避免被播放栏遮挡

          .song-row {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-radius: 4px;
            transition: background-color 0.2s;
            cursor: pointer;

            &:hover {
              background: rgba(255, 255, 255, 0.05);
            }

            &.active {
              background: rgba(236, 65, 65, 0.1);
              color: #ec4141;
            }

            &.playing {
              .song-name {
                color: #ec4141;
              }
            }

            .col-index {
              width: 60px;
              text-align: center;
              font-size: 14px;
              opacity: 0.6;

              .play-btn,
              .pause-btn {
                font-size: 16px;
                color: #ec4141;
                cursor: pointer;
              }
            }

            .col-title {
              flex: 2;
              padding-left: 16px;

              .song-info {
                .song-name {
                  font-size: 14px;
                  font-weight: 500;
                  margin-bottom: 4px;
                }

                .song-tags {
                  display: flex;
                  gap: 4px;

                  .tag {
                    background: rgba(236, 65, 65, 0.2);
                    color: #ec4141;
                    padding: 2px 6px;
                    border-radius: 2px;
                    font-size: 10px;
                  }
                }
              }
            }

            .col-artist {
              flex: 1;
              font-size: 14px;
              opacity: 0.8;

              .artist-name {
                cursor: pointer;

                &:hover {
                  color: #4a9eff;
                }
              }
            }

            .col-album {
              flex: 1;
              font-size: 14px;
              opacity: 0.8;

              .album-name {
                cursor: pointer;

                &:hover {
                  color: #4a9eff;
                }
              }
            }

            .col-duration {
              width: 200px;
              text-align: right;
              padding-right: 16px;

              .duration-actions {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                gap: 12px;
                opacity: 0;
                transition: opacity 0.2s;

                .like-btn {
                  font-size: 16px;
                  cursor: pointer;
                  color: var(--text-secondary);

                  &.liked {
                    color: #ec4141;
                  }

                  &:hover {
                    color: #ec4141;
                  }
                }

                .action-icon {
                  font-size: 14px;
                  cursor: pointer;
                  color: var(--text-secondary);

                  &:hover {
                    color: var(--text-primary);
                  }
                }

                .duration {
                  font-size: 14px;
                  opacity: 0.6;
                  min-width: 40px;
                }
              }
            }

            &:hover .duration-actions {
              opacity: 1;
            }
          }
        }
      }

      // 歌曲加载状态
      .loading-songs,
      .empty-songs {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: $spacing-xxl;
        color: var(--text-secondary);
        gap: $spacing-sm;

        .loading-icon {
          font-size: 20px;
          animation: spin 1s linear infinite;
        }
      }

      .empty-songs {
        font-size: $font-size-lg;
        opacity: 0.7;
      }

      // 加载更多区域
      .load-more-section {
        padding: $spacing-xl $spacing-md;
        text-align: center;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        margin-top: $spacing-lg;

        .loading-more,
        .has-more,
        .no-more {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: $spacing-sm;
          color: var(--text-secondary);
          font-size: $font-size-sm;
        }

        .loading-more {
          color: var(--primary-color);

          .loading-icon {
            font-size: 16px;
            animation: spin 1s linear infinite;
          }
        }

        .has-more {
          opacity: 0.6;
        }

        .no-more {
          opacity: 0.5;
          font-style: italic;
        }
      }
    }
  }



}
</style>