import { defineStore } from 'pinia'
import { ref } from 'vue'
import request from '../utils/request'

// SongInfo 对象结构:
// {
//     id: string
//     title: string
//     artist: string
//     coverUrl: string
//     audioUrl: string
//     liked: boolean
// }

export const useMusicStore = defineStore('music', () => {
    // 状态
    // --------------------播放器基础状态--------------------
    const currentSong = ref(null)
    const isPlaying = ref(false)
    const playlist = ref([])  // 当前播放列表
    const currentIndex = ref(0)  // 当前播放索引
    const volume = ref(100)
    const loading = ref(false)  // 播放加载状态
    const error = ref('')  // 播放错误信息

    // 播放模式：'order' | 'random' | 'single'
    const playMode = ref('order')

    // 音频元素引用（由组件设置）
    const audioElement = ref(null)

    // 播放历史
    const playHistory = ref(JSON.parse(localStorage.getItem('music_play_history') || '[]'))

    // 播放质量设置
    const playQuality = ref('exhigh')  // 默认极高音质

    // 歌词数据
    const currentLyrics = ref(null)  // 当前歌曲歌词
    const lyricsLoading = ref(false)  // 歌词加载状态

    // --------------------播放控制功能--------------------

    /**
     * 播放歌曲（支持歌曲ID或歌曲对象）
     * @param {string|number|Object} songOrId - 歌曲ID或歌曲对象
     * @param {Array} playlistSongs - 可选的播放列表（如果从歌单播放）
     */
    async function playSong(songOrId, playlistSongs = null) {
        try {
            loading.value = true
            error.value = ''

            let songToPlay = null

            // 处理参数：如果是ID，获取歌曲详情；如果是对象，检查是否需要补充信息
            if (typeof songOrId === 'string' || typeof songOrId === 'number') {
                console.log('通过ID播放歌曲:', songOrId)
                songToPlay = await fetchSongDetail(songOrId)
            } else if (songOrId && typeof songOrId === 'object') {
                console.log('通过对象播放歌曲:', songOrId)

                // 检查歌曲对象是否缺少封面信息，如果缺少则重新获取详情
                if (!songOrId.coverUrl && !songOrId.al?.picUrl && !songOrId.album?.picUrl) {
                    console.log('歌曲对象缺少封面信息，重新获取详情:', songOrId.id)
                    try {
                        const detailedSong = await fetchSongDetail(songOrId.id)
                        // 合并原有信息和新获取的详情
                        songToPlay = {
                            ...songOrId,
                            ...detailedSong,
                            // 保留原有的一些可能有用的字段
                            audioUrl: songOrId.audioUrl,
                            bitrate: songOrId.bitrate,
                            fileSize: songOrId.fileSize,
                            urlData: songOrId.urlData
                        }
                    } catch (error) {
                        console.warn('重新获取歌曲详情失败，使用原对象:', error)
                        songToPlay = songOrId
                    }
                } else {
                    songToPlay = songOrId
                }
            } else {
                throw new Error('无效的歌曲参数')
            }

            // 检查播放权限
            if (songToPlay.privilege && !songToPlay.privilege.canPlay) {
                throw new Error('该歌曲暂无播放权限')
            }

            // 获取播放URL
            console.log('获取播放URL...')
            const urlData = await fetchSongUrlV1(songToPlay.id, playQuality.value)

            if (!urlData.available || !urlData.url) {
                throw new Error('该歌曲暂无可用播放链接')
            }

            // 合并歌曲信息和播放URL
            const playableSong = {
                ...songToPlay,
                audioUrl: urlData.url,
                bitrate: urlData.br,
                fileSize: urlData.size,
                urlData: urlData
            }

            // 更新播放列表（如果提供了新的播放列表）
            if (playlistSongs && Array.isArray(playlistSongs)) {
                playlist.value = playlistSongs
                currentIndex.value = playlistSongs.findIndex(song =>
                    song.id === playableSong.id || song.id === songOrId
                )
            } else if (playlist.value.length === 0) {
                // 如果没有播放列表，创建单曲播放列表
                playlist.value = [playableSong]
                currentIndex.value = 0
            } else {
                // 更新当前索引
                const existingIndex = playlist.value.findIndex(song =>
                    song.id === playableSong.id
                )
                if (existingIndex >= 0) {
                    currentIndex.value = existingIndex
                    playlist.value[existingIndex] = playableSong
                } else {
                    // 添加到播放列表
                    playlist.value.push(playableSong)
                    currentIndex.value = playlist.value.length - 1
                }
            }

            // 设置当前歌曲
            currentSong.value = playableSong
            isPlaying.value = true

            // 添加到播放历史
            addToPlayHistory(playableSong)

            console.log('歌曲播放准备完成:', playableSong.title)
            return playableSong

        } catch (err) {
            console.error('播放歌曲失败:', err)
            error.value = err.message || '播放失败'
            isPlaying.value = false
            throw err
        } finally {
            loading.value = false
        }
    }

    /**
     * 播放整个歌单
     * @param {Array} songs - 歌曲列表
     * @param {number} startIndex - 开始播放的索引，默认0
     */
    async function playPlaylist(songs, startIndex = 0) {
        if (!songs || songs.length === 0) {
            throw new Error('播放列表为空')
        }

        const songToPlay = songs[startIndex] || songs[0]
        await playSong(songToPlay, songs)
    }

    /**
     * 切换播放/暂停
     */
    function togglePlay() {
        if (!currentSong.value) {
            console.warn('没有当前播放歌曲')
            return
        }

        isPlaying.value = !isPlaying.value

        // 如果有音频元素，控制播放/暂停
        if (audioElement.value) {
            if (isPlaying.value) {
                audioElement.value.play().catch(err => {
                    console.error('播放失败:', err)
                    isPlaying.value = false
                })
            } else {
                audioElement.value.pause()
            }
        }
    }

    /**
     * 下一首歌
     */
    async function nextSong() {
        if (playlist.value.length === 0) {
            console.warn('播放列表为空')
            return
        }

        let nextIndex

        if (playMode.value === 'random') {
            // 随机播放
            nextIndex = Math.floor(Math.random() * playlist.value.length)
        } else if (playMode.value === 'single') {
            // 单曲循环
            nextIndex = currentIndex.value
        } else {
            // 顺序播放
            nextIndex = (currentIndex.value + 1) % playlist.value.length
        }

        const nextSong = playlist.value[nextIndex]
        if (nextSong) {
            currentIndex.value = nextIndex
            await playSong(nextSong)
        }
    }

    /**
     * 上一首歌
     */
    async function prevSong() {
        if (playlist.value.length === 0) {
            console.warn('播放列表为空')
            return
        }

        let prevIndex

        if (playMode.value === 'random') {
            // 随机播放
            prevIndex = Math.floor(Math.random() * playlist.value.length)
        } else if (playMode.value === 'single') {
            // 单曲循环
            prevIndex = currentIndex.value
        } else {
            // 顺序播放
            prevIndex = (currentIndex.value - 1 + playlist.value.length) % playlist.value.length
        }

        const prevSongItem = playlist.value[prevIndex]
        if (prevSongItem) {
            currentIndex.value = prevIndex
            await playSong(prevSongItem)
        }
    }

    /**
     * 设置音频元素引用
     */
    function setAudioElement(element) {
        audioElement.value = element
    }

    /**
     * 添加到播放历史
     */
    function addToPlayHistory(song) {
        // 移除已存在的记录
        const existingIndex = playHistory.value.findIndex(item => item.id === song.id)
        if (existingIndex >= 0) {
            playHistory.value.splice(existingIndex, 1)
        }

        // 添加到开头
        playHistory.value.unshift({
            ...song,
            playTime: Date.now()
        })

        // 限制历史记录数量
        if (playHistory.value.length > 100) {
            playHistory.value = playHistory.value.slice(0, 100)
        }

        // 持久化
        localStorage.setItem('music_play_history', JSON.stringify(playHistory.value))
    }

    /**
     * 清空播放列表
     */
    function clearPlaylist() {
        playlist.value = []
        currentSong.value = null
        currentIndex.value = 0
        isPlaying.value = false
    }

    /**
     * 从播放列表移除歌曲
     */
    function removeSongFromPlaylist(index) {
        if (index < 0 || index >= playlist.value.length) return

        // 如果删除的是当前播放歌曲
        if (index === currentIndex.value) {
            if (playlist.value.length === 1) {
                // 只有一首歌，清空播放列表
                clearPlaylist()
            } else {
                // 播放下一首
                nextSong()
            }
        } else if (index < currentIndex.value) {
            // 删除的歌曲在当前歌曲之前，更新索引
            currentIndex.value--
        }

        playlist.value.splice(index, 1)
    }

    /**
     * 切换喜欢状态
     */
    function toggleLike(songId) {
        // 更新播放列表中的歌曲
        const song = playlist.value.find(song => song.id === songId)
        if (song) {
            song.liked = !song.liked
        }

        // 更新当前歌曲
        if (currentSong.value && currentSong.value.id === songId) {
            currentSong.value.liked = !currentSong.value.liked
        }

        // 更新播放历史
        const historyItem = playHistory.value.find(item => item.id === songId)
        if (historyItem) {
            historyItem.liked = !historyItem.liked
        }
    }

    /**
     * 检查歌曲是否被喜欢
     */
    function isLiked(songId) {
        const song = playlist.value.find(song => song.id === songId)
        return song ? (song.liked || song.isLiked || false) : false
    }

    /**
     * 设置音量
     */
    function setVolume(newVolume) {
        volume.value = newVolume

        if (audioElement.value) {
            audioElement.value.volume = newVolume / 100
        }
    }

    /**
     * 设置播放模式
     */
    function setPlayMode(mode) {
        playMode.value = mode
    }

    /**
     * 设置播放质量
     */
    function setPlayQuality(quality) {
        playQuality.value = quality
    }

    // --------------------API 接口调用--------------------

    /**
     * 获取歌曲详情
     * @param {string|number|Array} ids - 歌曲ID，支持单个ID或多个ID数组
     * @returns {Promise<Object>} 歌曲详情数据
     */
    async function fetchSongDetail(ids) {
        try {
            // 处理参数：支持单个ID或ID数组
            const idsParam = Array.isArray(ids) ? ids.join(',') : String(ids)

            console.log('获取歌曲详情:', idsParam)

            const response = await request.get('/song/detail', {
                params: { ids: idsParam }
            })

            console.log('歌曲详情API响应:', response)

            if (response && response.songs) {
                // 格式化歌曲数据
                const formattedSongs = response.songs.map(song => formatSongData(song, response.privileges))

                // 如果只请求一首歌，返回单个对象；否则返回数组
                return Array.isArray(ids) ? formattedSongs : formattedSongs[0]
            } else {
                throw new Error('歌曲数据格式错误')
            }
        } catch (error) {
            console.error('获取歌曲详情失败:', error)
            throw error
        }
    }

    /**
     * 格式化歌曲数据
     * @param {Object} song - 原始歌曲数据
     * @param {Array} privileges - 权限数据数组
     * @returns {Object} 格式化后的歌曲数据
     */
    function formatSongData(song, privileges = []) {
        // 查找对应的权限信息
        const privilege = privileges.find(p => p.id === song.id) || {}

        return {
            id: song.id,
            title: song.name,
            artist: song.ar?.map(artist => artist.name).join(' / ') || '未知歌手',
            artistList: song.ar || [],
            album: song.al?.name || '未知专辑',
            albumId: song.al?.id || 0,
            coverUrl: song.al?.picUrl || '',
            duration: song.dt || 0,
            durationText: formatDuration(song.dt),
            // 音质信息
            qualities: {
                hr: song.hr,      // Hi-Res
                sq: song.sq,      // 无损
                h: song.h,        // 高品质
                m: song.m,        // 中品质
                l: song.l         // 低品质
            },
            // 权限信息
            privilege: {
                canPlay: privilege.st >= 0,
                canDownload: privilege.dl > 0,
                maxBitrate: privilege.maxbr || 0,
                fee: song.fee || 0,
                toast: privilege.toast || false
            },
            // 其他信息
            mvId: song.mv || 0,
            hasMv: (song.mv || 0) > 0,
            popularity: song.pop || 0,
            publishTime: song.publishTime || 0,
            liked: false,
            // 原始数据（备用）
            raw: song
        }
    }

    /**
     * 格式化时长（毫秒转为 mm:ss 格式）
     * @param {number} duration - 时长（毫秒）
     * @returns {string} 格式化后的时长
     */
    function formatDuration(duration) {
        if (!duration) return '00:00'

        const minutes = Math.floor(duration / 60000)
        const seconds = Math.floor((duration % 60000) / 1000)

        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }

    /**
     * 获取音乐播放URL（旧版接口）
     * @param {string|number|Array} ids - 歌曲ID，支持单个ID或多个ID数组
     * @param {number} br - 码率，默认999000（最大码率）
     * @returns {Promise<Object|Array>} 音乐URL数据
     */
    async function fetchSongUrl(ids, br = 999000) {
        try {
            // 处理参数：支持单个ID或ID数组
            const idsParam = Array.isArray(ids) ? ids.join(',') : String(ids)

            console.log('获取音乐URL:', { ids: idsParam, br })

            const response = await request.get('/song/url', {
                params: {
                    id: idsParam,
                    br: br
                }
            })

            console.log('音乐URL API响应:', response)

            if (response && response.data) {
                // 格式化URL数据
                const formattedUrls = response.data.map(item => formatUrlData(item))

                // 如果只请求一首歌，返回单个对象；否则返回数组
                return Array.isArray(ids) ? formattedUrls : formattedUrls[0]
            } else {
                throw new Error('音乐URL数据格式错误')
            }
        } catch (error) {
            console.error('获取音乐URL失败:', error)
            throw error
        }
    }

    /**
     * 获取音乐播放URL（新版接口）
     * @param {string|number|Array} ids - 歌曲ID，支持单个ID或多个ID数组
     * @param {string} level - 播放音质等级
     * @returns {Promise<Object|Array>} 音乐URL数据
     */
    async function fetchSongUrlV1(ids, level = 'exhigh') {
        try {
            // 处理参数：支持单个ID或ID数组
            const idsParam = Array.isArray(ids) ? ids.join(',') : String(ids)

            console.log('获取音乐URL(v1):', { ids: idsParam, level })

            const response = await request.get('/song/url/v1', {
                params: {
                    id: idsParam,
                    level: level
                }
            })

            console.log('音乐URL(v1) API响应:', response)

            if (response && response.data) {
                // 格式化URL数据
                const formattedUrls = response.data.map(item => formatUrlData(item))

                // 如果只请求一首歌，返回单个对象；否则返回数组
                return Array.isArray(ids) ? formattedUrls : formattedUrls[0]
            } else {
                throw new Error('音乐URL数据格式错误')
            }
        } catch (error) {
            console.error('获取音乐URL(v1)失败:', error)
            throw error
        }
    }

    /**
     * 检查音乐是否可用
     * @param {string|number} id - 歌曲ID
     * @param {number} br - 码率，默认999000
     * @returns {Promise<Object>} 可用性检查结果
     */
    async function checkMusicAvailable(id, br = 999000) {
        try {
            console.log('检查音乐可用性:', { id, br })

            const response = await request.get('/check/music', {
                params: {
                    id: String(id),
                    br: br
                }
            })

            console.log('音乐可用性检查响应:', response)

            return {
                id: id,
                available: response.success || false,
                message: response.message || '检查失败',
                br: br
            }
        } catch (error) {
            console.error('检查音乐可用性失败:', error)
            return {
                id: id,
                available: false,
                message: error.message || '检查失败',
                br: br
            }
        }
    }

    /**
     * 获取歌词
     * @param {string|number} id - 歌曲ID
     * @returns {Promise<Object>} 歌词数据
     */
    async function fetchLyrics(id) {
        try {
            lyricsLoading.value = true
            console.log('获取歌词:', id)

            const response = await request.get('/lyric', {
                params: { id: String(id) }
            })

            console.log('歌词API响应:', response)

            // 解析歌词数据
            const lyricsData = {
                id: String(id),
                lrc: response.lrc || null,
                tlyric: response.tlyric || null,
                romalrc: response.romalrc || null,
                parsedLyrics: []
            }

            // 解析歌词文本为时间轴格式
            if (response.lrc && response.lrc.lyric) {
                lyricsData.parsedLyrics = parseLyricText(response.lrc.lyric)
            }

            currentLyrics.value = lyricsData
            return lyricsData

        } catch (error) {
            console.error('获取歌词失败:', error)
            currentLyrics.value = null
            throw error
        } finally {
            lyricsLoading.value = false
        }
    }

    /**
     * 解析歌词文本
     * @param {string} lyricText - 歌词文本
     * @returns {Array} 解析后的歌词数组
     */
    function parseLyricText(lyricText) {
        if (!lyricText) return []

        const lines = lyricText.split('\n')
        const lyrics = []

        lines.forEach(line => {
            // 匹配时间标签格式 [mm:ss.xx] 或 [mm:ss.xxx]
            const timeMatch = line.match(/\[(\d{1,2}):(\d{2})\.(\d{2,3})\](.*)/)
            if (timeMatch) {
                const minutes = parseInt(timeMatch[1])
                const seconds = parseInt(timeMatch[2])
                const milliseconds = parseInt(timeMatch[3].padEnd(3, '0'))
                const text = timeMatch[4].trim()

                // 计算总秒数
                const totalSeconds = minutes * 60 + seconds + milliseconds / 1000

                // 添加所有行，包括空行（用于时间对齐）
                lyrics.push({
                    time: totalSeconds,
                    text: text || '', // 空文本也保留
                    isEmpty: !text // 标记是否为空行
                })
            }
        })

        // 按时间排序
        const sortedLyrics = lyrics.sort((a, b) => a.time - b.time)

        // 过滤掉作词作曲信息和空行（但保留有意义的空行用于间隔）
        const filteredLyrics = sortedLyrics.filter(lyric => {
            const text = lyric.text.toLowerCase()
            return !text.includes('作词') &&
                   !text.includes('作曲') &&
                   !text.includes('编曲') &&
                   !text.includes('制作人') &&
                   (lyric.text || lyric.isEmpty) // 保留有文本的行或标记为空的行
        })

        return filteredLyrics
    }

    /**
     * 格式化URL数据
     * @param {Object} urlData - 原始URL数据
     * @returns {Object} 格式化后的URL数据
     */
    function formatUrlData(urlData) {
        return {
            id: urlData.id,
            url: urlData.url || '',
            br: urlData.br || 0,
            size: urlData.size || 0,
            md5: urlData.md5 || '',
            code: urlData.code || 0,
            expi: urlData.expi || 0,
            type: urlData.type || '',
            gain: urlData.gain || 0,
            peak: urlData.peak || 0,
            fee: urlData.fee || 0,
            uf: urlData.uf || null,
            payed: urlData.payed || 0,
            flag: urlData.flag || 0,
            canExtend: urlData.canExtend || false,
            freeTrialInfo: urlData.freeTrialInfo || null,
            level: urlData.level || '',
            encodeType: urlData.encodeType || '',
            // 试听片段信息
            freeTimeTrialPrivilege: urlData.freeTimeTrialPrivilege || {
                resConsumable: false,
                userConsumable: false,
                type: 0,
                remainTime: 0
            },
            // 可用性判断
            available: !!(urlData.url && urlData.code === 200),
            // 原始数据
            raw: urlData
        }
    }

    return {
        // 状态
        currentSong,
        isPlaying,
        playlist,
        currentIndex,
        volume,
        loading,
        error,
        playMode,
        playHistory,
        playQuality,
        currentLyrics,
        lyricsLoading,

        // 播放控制方法
        playSong,
        playPlaylist,
        togglePlay,
        nextSong,
        prevSong,
        setAudioElement,
        clearPlaylist,
        removeSongFromPlaylist,

        // 设置方法
        toggleLike,
        isLiked,
        setVolume,
        setPlayMode,
        setPlayQuality,

        // API 方法
        fetchSongDetail,
        fetchSongUrl,
        fetchSongUrlV1,
        checkMusicAvailable,
        fetchLyrics,
        formatSongData,
        formatUrlData,
        formatDuration,
        parseLyricText,

        // 工具方法
        addToPlayHistory
    }
})