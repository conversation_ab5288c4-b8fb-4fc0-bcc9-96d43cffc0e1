import { defineStore } from 'pinia';
import { ref } from 'vue';
import request from '../utils/request';

export const usePlaylistStore = defineStore('playlist', () => {
  // 页面渲染状态
  const playlists = ref([]); // 当前页面显示的歌单数据
  const loading = ref(false);
  const error = ref(null);
  const hasMore = ref(true);
  const lastUpdateTime = ref(null);

  // 分类数据缓存状态
  const categoryPlaylists = ref(new Map()); // 存储每个分类的歌单数据
  const cacheTimestamp = ref(new Map()); // 存储每个分类的缓存时间戳
  const currentCategory = ref('全部'); // 当前选中的分类
  const showRefreshTip = ref(false); // 是否显示刷新提示

  // 获取精品歌单
  async function fetchHighQualityPlaylists(options = {}) {
    const {
      cat = '全部',
      limit = 28,
      before = null,
      reset = false
    } = options;

    // 如果是重置，清空现有数据
    if (reset) {
      playlists.value = [];
      lastUpdateTime.value = null;
      hasMore.value = true;
    }

    loading.value = true;
    error.value = null;

    try {
      console.log('获取精品歌单:', { cat, limit, before });
      
      // 构建请求参数
      const params = {
        limit
      };
      
      // 如果不是"全部"分类，添加cat参数
      if (cat !== '全部' && cat !== 'all') {
        params.cat = cat;
      }
      
      // 如果有before参数，添加分页参数
      if (before || lastUpdateTime.value) {
        params.before = before || lastUpdateTime.value;
      }

      const response = await request.get('/top/playlist/highquality', { params });
      console.log('精品歌单API响应:', response);

      if (response && response.playlists) {
        // 先创建基础歌单数据
        const newPlaylists = response.playlists.map(playlist => ({
          id: playlist.id,
          title: playlist.name,
          description: playlist.description || playlist.copywriter || '',
          coverUrl: playlist.coverImgUrl,
          tag: cat, // 直接使用请求的分类名称
          playCount: formatPlayCount(playlist.playCount),
          creator: playlist.creator?.nickname || '未知',
          updateTime: playlist.updateTime,
          trackCount: playlist.trackCount || 0, // 歌曲总数
          previewSongs: [] // 预览歌曲，将在下面填充
        }));

        // 并发获取所有歌单的预览歌曲
        console.log(`开始获取 ${newPlaylists.length} 个歌单的预览歌曲`);
        const previewResults = await Promise.allSettled(
          newPlaylists.map(async (playlist) => {
            try {
              const previewSongs = await fetchPlaylistPreviewSongs(playlist.id);
              playlist.previewSongs = previewSongs;
              console.log(`歌单 ${playlist.title} 预览歌曲:`, previewSongs.length, '首');
              return { success: true, id: playlist.id, count: previewSongs.length };
            } catch (error) {
              console.error(`获取歌单 ${playlist.id} 预览歌曲失败:`, error);
              playlist.previewSongs = [];
              return { success: false, id: playlist.id, error };
            }
          })
        );

        const successCount = previewResults.filter(r => r.value?.success).length;
        console.log(`预览歌曲获取完成: ${successCount}/${newPlaylists.length} 成功`);

        console.log('格式化后的歌单数据:', newPlaylists.slice(0, 3).map(p => ({
          title: p.title,
          tag: p.tag
        })));

        if (reset) {
          playlists.value = newPlaylists;
        } else {
          playlists.value.push(...newPlaylists);
        }

        // 更新分页信息
        if (newPlaylists.length > 0) {
          lastUpdateTime.value = newPlaylists[newPlaylists.length - 1].updateTime;
        }
        
        // 如果返回的数据少于请求的数量，说明没有更多数据了
        hasMore.value = newPlaylists.length === limit;

        console.log('精品歌单获取成功，数量:', newPlaylists.length);
      } else {
        throw new Error('精品歌单数据格式错误');
      }
    } catch (err) {
      console.error('获取精品歌单失败:', err);
      error.value = err.message || '获取精品歌单失败';
      
      // 如果是首次加载失败，使用默认数据
      if (reset && playlists.value.length === 0) {
        playlists.value = getDefaultPlaylists();
      }
    } finally {
      loading.value = false;
    }

    return playlists.value;
  }

  // 获取推荐歌单
  async function fetchPersonalizedPlaylists(limit = 30) {
    try {
      console.log('获取推荐歌单:', { limit });

      const response = await request.get('/personalized', {
        params: { limit }
      });

      console.log('推荐歌单API响应:', response);

      if (response && response.result) {
        // 格式化推荐歌单数据
        const formattedPlaylists = response.result.map(playlist => ({
          id: playlist.id,
          title: playlist.name,
          description: playlist.copywriter || '',
          coverUrl: playlist.picUrl,
          tag: '推荐',
          playCount: formatPlayCount(playlist.playcount || playlist.playCount),
          creator: '网易云音乐',
          updateTime: Date.now(),
          trackCount: playlist.trackCount || 0,
          previewSongs: []
        }));

        // 并行获取所有歌单的预览歌曲
        console.log('开始获取推荐歌单的预览歌曲...');
        const playlistsWithPreview = await Promise.all(
          formattedPlaylists.map(async (playlist) => {
            try {
              const previewSongs = await fetchPlaylistPreviewSongs(playlist.id);
              return {
                ...playlist,
                previewSongs
              };
            } catch (error) {
              console.warn(`获取歌单 ${playlist.id} 预览歌曲失败:`, error);
              return playlist; // 返回原始数据，预览歌曲为空数组
            }
          })
        );

        console.log('推荐歌单及预览歌曲获取完成，数量:', playlistsWithPreview.length);
        return playlistsWithPreview;
      } else {
        throw new Error('推荐歌单数据格式错误');
      }
    } catch (error) {
      console.error('获取推荐歌单失败:', error);
      throw error;
    }
  }

  // 根据分类获取歌单（带缓存）
  async function fetchPlaylistsByCategory(category, forceRefresh = false) {
    currentCategory.value = category;

    // 检查该分类是否有缓存数据
    if (!forceRefresh && categoryPlaylists.value.has(category)) {
      console.log(`使用缓存数据 - 分类: ${category}`);

      // 直接从缓存更新页面显示数据
      playlists.value = [...categoryPlaylists.value.get(category)];

      // 检查缓存时间，决定是否显示刷新提示
      const categoryTimestamp = cacheTimestamp.value.get(category);
      if (categoryTimestamp) {
        const now = Date.now();
        const oneHour = 60 * 60 * 1000;
        showRefreshTip.value = (now - categoryTimestamp) > oneHour;
      }

      return playlists.value;
    }

    // 从API获取数据
    console.log(`从API获取数据 - 分类: ${category}`);
    loading.value = true;

    try {
      let result;

      // 如果是"全部"分类，使用推荐歌单API
      if (category === '全部' || category === 'all') {
        result = await fetchPersonalizedPlaylists();
      } else {
        // 其他分类使用精品歌单API
        result = await fetchHighQualityPlaylists({
          cat: category,
          limit: 28,
          reset: true
        });
      }

      // 存储到分类缓存
      if (result && result.length > 0) {
        categoryPlaylists.value.set(category, [...result]);
        cacheTimestamp.value.set(category, Date.now());

        // 更新页面显示数据
        playlists.value = [...result];

        // 持久化缓存到localStorage
        saveCacheToStorage();

        showRefreshTip.value = false;
        console.log(`数据已缓存 - 分类: ${category}, 数量: ${result.length}`);
      }

      return playlists.value;
    } finally {
      loading.value = false;
    }
  }

  // 加载更多歌单
  async function loadMorePlaylists(category = '全部') {
    if (!hasMore.value || loading.value) {
      return;
    }

    return await fetchHighQualityPlaylists({
      cat: category,
      limit: 14,
      before: lastUpdateTime.value,
      reset: false
    });
  }

  // 获取歌单预览歌曲（前3首）
  async function fetchPlaylistPreviewSongs(playlistId) {
    try {
      const response = await request.get('/playlist/track/all', {
        params: {
          id: playlistId,
          limit: 3,
          offset: 0
        }
      });

      if (response && response.songs && Array.isArray(response.songs)) {
        return response.songs.slice(0, 3).map(song => ({
          id: song.id,
          name: song.name,
          artist: song.ar?.[0]?.name || '未知歌手',
          album: song.al?.name || '未知专辑'
        }));
      }
      return [];
    } catch (error) {
      console.error(`获取歌单 ${playlistId} 预览歌曲失败:`, error);
      return [];
    }
  }

  // 格式化播放次数
  function formatPlayCount(count) {
    if (!count) return '0';
    
    if (count >= 100000000) {
      return Math.floor(count / 100000000) + '亿';
    } else if (count >= 10000) {
      return Math.floor(count / 10000) + '万';
    } else {
      return count.toString();
    }
  }

  // 默认歌单数据（API失败时使用）
  function getDefaultPlaylists() {
    return [
      {
        id: 1,
        title: '抖音爆火动感曲',
        description: '最新最热的抖音神曲合集',
        coverUrl: 'https://picsum.photos/200/200?random=1',
        tag: '热门',
        playCount: '1000万',
        creator: '音乐推荐',
        songs: []
      },
      {
        id: 2,
        title: '热门DJ',
        description: '电音狂欢，燃爆全场',
        coverUrl: 'https://picsum.photos/200/200?random=2',
        tag: '电子',
        playCount: '500万',
        creator: 'DJ推荐',
        songs: []
      }
    ];
  }

  // 保存缓存到localStorage
  function saveCacheToStorage() {
    try {
      const cacheData = {
        categoryPlaylists: Object.fromEntries(categoryPlaylists.value),
        cacheTimestamp: Object.fromEntries(cacheTimestamp.value)
      };
      localStorage.setItem('playlist_category_cache', JSON.stringify(cacheData));
      console.log('缓存已保存到localStorage');
    } catch (error) {
      console.error('保存缓存失败:', error);
    }
  }

  // 从localStorage加载缓存
  function loadCacheFromStorage() {
    try {
      const savedCache = localStorage.getItem('playlist_category_cache');
      if (savedCache) {
        const cacheData = JSON.parse(savedCache);
        categoryPlaylists.value = new Map(Object.entries(cacheData.categoryPlaylists || {}));
        cacheTimestamp.value = new Map(Object.entries(cacheData.cacheTimestamp || {}));
        console.log('缓存已从localStorage加载，分类数量:', categoryPlaylists.value.size);
        return true;
      }
    } catch (error) {
      console.error('加载缓存失败:', error);
    }
    return false;
  }

  // 强制刷新当前分类数据
  async function forceRefreshCurrentCategory() {
    showRefreshTip.value = false;
    return await fetchPlaylistsByCategory(currentCategory.value, true);
  }

  // 检查是否需要显示刷新提示
  function checkRefreshTip() {
    if (cacheTimestamp.value) {
      const now = Date.now();
      const cacheAge = now - cacheTimestamp.value;
      const oneHour = 60 * 60 * 1000; // 1小时

      // 如果缓存超过1小时，显示刷新提示
      if (cacheAge > oneHour) {
        showRefreshTip.value = true;
      }
    }
  }

  // 清除所有缓存
  function clearAllCache() {
    categoryPlaylists.value.clear();
    cacheTimestamp.value.clear();
    showRefreshTip.value = false;
    localStorage.removeItem('playlist_category_cache');
    console.log('所有缓存已清除');
  }

  // 清除数据
  function clearPlaylists() {
    playlists.value = [];
    lastUpdateTime.value = null;
    hasMore.value = true;
    error.value = null;
  }

  // 初始化缓存（应用启动时调用）
  function initializeCache() {
    const loaded = loadCacheFromStorage();
    if (loaded) {
      checkRefreshTip();
    }
    return loaded;
  }

  return {
    playlists,
    loading,
    error,
    hasMore,
    lastUpdateTime,
    categoryPlaylists,
    currentCategory,
    showRefreshTip,
    fetchHighQualityPlaylists,
    fetchPersonalizedPlaylists,
    fetchPlaylistsByCategory,
    loadMorePlaylists,
    fetchPlaylistPreviewSongs,
    forceRefreshCurrentCategory,
    clearAllCache,
    clearPlaylists,
    initializeCache
  };
});
