<template>
  <div :class="loadingClasses">
    <div class="loading-content">
      <!-- 音乐波形加载动画 -->
      <div v-if="type === 'wave'" class="loading-wave">
        <div 
          v-for="i in 5" 
          :key="i" 
          class="wave-bar"
          :style="{ animationDelay: `${(i - 1) * 0.1}s` }"
        ></div>
      </div>
      
      <!-- 旋转唱片加载动画 -->
      <div v-else-if="type === 'vinyl'" class="loading-vinyl">
        <div class="vinyl-disc">
          <div class="vinyl-center"></div>
        </div>
      </div>
      
      <!-- 脉冲圆点加载动画 -->
      <div v-else-if="type === 'dots'" class="loading-dots">
        <div 
          v-for="i in 3" 
          :key="i" 
          class="dot"
          :style="{ animationDelay: `${(i - 1) * 0.2}s` }"
        ></div>
      </div>
      
      <!-- 默认旋转加载动画 -->
      <div v-else class="loading-spinner">
        <div class="spinner-ring"></div>
      </div>
      
      <!-- 加载文字 -->
      <div v-if="text" class="loading-text">{{ text }}</div>
    </div>
    
    <!-- 遮罩层 -->
    <div v-if="overlay" class="loading-overlay"></div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  // 加载动画类型
  type: {
    type: String,
    default: 'spinner',
    validator: (value) => ['spinner', 'wave', 'vinyl', 'dots'].includes(value)
  },
  // 尺寸
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  // 加载文字
  text: {
    type: String,
    default: ''
  },
  // 是否显示遮罩层
  overlay: {
    type: Boolean,
    default: false
  },
  // 是否居中显示
  centered: {
    type: Boolean,
    default: false
  },
  // 自定义颜色
  color: {
    type: String,
    default: ''
  }
});

const loadingClasses = computed(() => [
  'ui-loading',
  `ui-loading--${props.size}`,
  {
    'ui-loading--centered': props.centered,
    'ui-loading--overlay': props.overlay
  }
]);
</script>

<style lang="scss" scoped>
@import '../../styles/design-system.scss';

.ui-loading {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  &--centered {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: $z-modal;
  }
  
  &--overlay {
    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--bg-overlay);
      z-index: -1;
    }
  }
  
  // 尺寸变体
  &--small {
    .loading-content {
      font-size: $font-size-sm;
    }
    
    .loading-spinner .spinner-ring {
      width: 20px;
      height: 20px;
    }
    
    .loading-wave .wave-bar {
      width: 3px;
      height: 20px;
    }
    
    .loading-vinyl .vinyl-disc {
      width: 30px;
      height: 30px;
    }
    
    .loading-dots .dot {
      width: 6px;
      height: 6px;
    }
  }
  
  &--medium {
    .loading-content {
      font-size: $font-size-md;
    }
    
    .loading-spinner .spinner-ring {
      width: 32px;
      height: 32px;
    }
    
    .loading-wave .wave-bar {
      width: 4px;
      height: 32px;
    }
    
    .loading-vinyl .vinyl-disc {
      width: 48px;
      height: 48px;
    }
    
    .loading-dots .dot {
      width: 8px;
      height: 8px;
    }
  }
  
  &--large {
    .loading-content {
      font-size: $font-size-lg;
    }
    
    .loading-spinner .spinner-ring {
      width: 48px;
      height: 48px;
    }
    
    .loading-wave .wave-bar {
      width: 6px;
      height: 48px;
    }
    
    .loading-vinyl .vinyl-disc {
      width: 64px;
      height: 64px;
    }
    
    .loading-dots .dot {
      width: 12px;
      height: 12px;
    }
  }
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-md;
  color: var(--text-primary);
}

.loading-text {
  font-size: inherit;
  color: var(--text-secondary);
}

// 旋转加载器
.loading-spinner {
  .spinner-ring {
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

// 音乐波形加载器
.loading-wave {
  display: flex;
  align-items: flex-end;
  gap: 2px;
  
  .wave-bar {
    background: linear-gradient(to top, var(--primary-color), var(--primary-hover));
    border-radius: 2px;
    animation: wave 1.2s ease-in-out infinite;
  }
}

// 唱片加载器
.loading-vinyl {
  .vinyl-disc {
    background: linear-gradient(45deg, #333, #666);
    border-radius: 50%;
    position: relative;
    animation: spin 2s linear infinite;
    
    .vinyl-center {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 30%;
      height: 30%;
      background: var(--bg-primary);
      border-radius: 50%;
      
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40%;
        height: 40%;
        background: var(--primary-color);
        border-radius: 50%;
      }
    }
  }
}

// 脉冲圆点加载器
.loading-dots {
  display: flex;
  gap: $spacing-xs;
  
  .dot {
    background: var(--primary-color);
    border-radius: 50%;
    animation: pulse-dot 1.4s ease-in-out infinite;
  }
}

// 动画定义
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes wave {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

@keyframes pulse-dot {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
