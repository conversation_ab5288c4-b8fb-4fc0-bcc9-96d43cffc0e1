/* 音乐播放器设计系统 */

/* ===== 颜色系统 ===== */
:root {
  /* 主色调 */
  --primary-color: #1890ff;
  --primary-hover: #40a9ff;
  --primary-active: #096dd9;
  --primary-light: rgba(24, 144, 255, 0.1);
  
  /* 功能色 */
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --info-color: #1890ff;
  
  /* 文字颜色 */
  --text-primary: #ffffff;
  --text-secondary: #aaaaaa;
  --text-disabled: #666666;
  --text-inverse: #000000;
  
  /* 背景色 */
  --bg-primary: #282c34;
  --bg-secondary: #333940;
  --bg-tertiary: #3a4049;
  --bg-card: #3e4552;
  --bg-overlay: rgba(0, 0, 0, 0.6);
  
  /* 边框色 */
  --border-color: rgba(255, 255, 255, 0.1);
  --border-hover: rgba(255, 255, 255, 0.2);
  --border-active: rgba(255, 255, 255, 0.3);
  
  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.4);
}

/* ===== 间距系统 ===== */
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

/* ===== 字体系统 ===== */
$font-size-xs: 10px;
$font-size-sm: 12px;
$font-size-md: 14px;
$font-size-lg: 16px;
$font-size-xl: 18px;
$font-size-xxl: 24px;
$font-size-xxxl: 32px;

$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-bold: 600;

/* ===== 圆角系统 ===== */
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;
$border-radius-full: 50%;

/* ===== 动画系统 ===== */
$transition-fast: 0.15s;
$transition-normal: 0.3s;
$transition-slow: 0.5s;

$easing-ease: ease;
$easing-ease-in: ease-in;
$easing-ease-out: ease-out;
$easing-ease-in-out: ease-in-out;

/* ===== 布局变量 ===== */
:root {
  --sidebar-width: 200px;
  --header-height: 48px;
  --player-controls-height: 60px;
  --content-max-width: 1200px;

  /* 计算视图区域高度（减去播放控制栏） */
  --view-height-with-player: calc(100vh - var(--player-controls-height));
  --view-height-full: 100vh;
}

/* ===== Z-index 层级 ===== */
$z-base: 1;
$z-dropdown: 10;
$z-sticky: 20;
$z-fixed: 30;
$z-modal-backdrop: 40;
$z-modal: 50;
$z-popover: 60;
$z-tooltip: 70;
$z-toast: 80;

:root {
  --z-base: #{$z-base};
  --z-dropdown: #{$z-dropdown};
  --z-sticky: #{$z-sticky};
  --z-fixed: #{$z-fixed};
  --z-modal-backdrop: #{$z-modal-backdrop};
  --z-modal: #{$z-modal};
  --z-popover: #{$z-popover};
  --z-tooltip: #{$z-tooltip};
  --z-toast: #{$z-toast};
  --z-player-controls: #{$z-fixed};
}

/* ===== 断点系统 ===== */
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 1024px;
$breakpoint-lg: 1280px;
$breakpoint-xl: 1536px;

/* ===== Mixins ===== */

// 居中对齐
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 水平居中
@mixin flex-center-horizontal {
  display: flex;
  justify-content: center;
}

// 垂直居中
@mixin flex-center-vertical {
  display: flex;
  align-items: center;
}

// 文字省略
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文字省略
@mixin text-ellipsis-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 过渡动画
@mixin transition($property: all, $duration: $transition-normal, $easing: $easing-ease) {
  transition: $property $duration $easing;
}

// 悬停效果
@mixin hover-lift {
  @include transition(transform);
  
  &:hover {
    transform: translateY(-2px);
  }
}

// 按钮基础样式
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: $border-radius-md;
  cursor: pointer;
  font-weight: $font-weight-medium;
  text-decoration: none;
  @include transition();
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// 卡片样式
@mixin card {
  background: var(--bg-card);
  border-radius: $border-radius-lg;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  @include transition();
  
  &:hover {
    border-color: var(--border-hover);
    box-shadow: var(--shadow-md);
  }
}

// 响应式断点
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-xs - 1px}) { @content; }
  }
  @if $breakpoint == sm {
    @media (min-width: #{$breakpoint-sm}) { @content; }
  }
  @if $breakpoint == md {
    @media (min-width: #{$breakpoint-md}) { @content; }
  }
  @if $breakpoint == lg {
    @media (min-width: #{$breakpoint-lg}) { @content; }
  }
  @if $breakpoint == xl {
    @media (min-width: #{$breakpoint-xl}) { @content; }
  }
}

// 滚动条样式
@mixin custom-scrollbar($width: 6px, $track-color: transparent, $thumb-color: rgba(255, 255, 255, 0.2)) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: calc($width / 2);
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

// 加载动画
@mixin loading-shimmer {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.1) 25%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// 脉冲动画
@mixin pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 旋转动画
@mixin rotate-animation($duration: 2s) {
  animation: rotate $duration linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ===== 工具类 ===== */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-center { @include flex-center; }
.flex-between { display: flex; justify-content: space-between; align-items: center; }
.flex-1 { flex: 1; }

.text-ellipsis { @include text-ellipsis; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.transition-all { @include transition(); }
.hover-lift { @include hover-lift; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }

.loading-shimmer { @include loading-shimmer; }
.pulse { @include pulse-animation; }
.rotate { @include rotate-animation; }

/* ===== 页面容器高度类 ===== */
.page-container {
  height: var(--view-height-full);
  overflow-y: auto;
  @include custom-scrollbar();
}

.page-container-with-player {
  height: var(--view-height-with-player);
  overflow-y: auto;
  @include custom-scrollbar();
}

/* ===== 全局组件样式覆盖 ===== */

// Ant Design Select 下拉菜单样式
.ant-select-dropdown {
  background: linear-gradient(135deg, rgba(20, 20, 30, 0.95), rgba(30, 30, 40, 0.95)) !important;
  backdrop-filter: blur(25px) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  border-radius: $border-radius-lg !important;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4), 0 4px 16px rgba(0, 0, 0, 0.2) !important;
  padding: 6px !important;

  .ant-select-item {
    color: var(--text-primary) !important;
    background: transparent !important;
    padding: 10px 14px !important;
    border-radius: $border-radius-md !important;
    margin: 2px 0 !important;
    font-size: $font-size-sm;

    &:hover {
      background: linear-gradient(135deg, rgba(24, 144, 255, 0.15), rgba(24, 144, 255, 0.08)) !important;
      color: var(--primary-color) !important;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    }

    &.ant-select-item-option-selected {
      background: linear-gradient(135deg, rgba(24, 144, 255, 0.25), rgba(24, 144, 255, 0.15)) !important;
      color: var(--primary-color) !important;
      font-weight: $font-weight-medium;
      border-left: 3px solid var(--primary-color);

      &:hover {
        background: linear-gradient(135deg, rgba(24, 144, 255, 0.3), rgba(24, 144, 255, 0.2)) !important;
      }
    }

    &.ant-select-item-option-active {
      background: rgba(24, 144, 255, 0.12) !important;
    }
  }

  .ant-select-item-empty {
    color: var(--text-secondary) !important;
    text-align: center;
    font-style: italic;
  }
}

// 特定的分类下拉框样式
.category-dropdown {
  .ant-select-item {
    &:hover {
      background: linear-gradient(135deg, rgba(24, 144, 255, 0.2), rgba(24, 144, 255, 0.1)) !important;
    }
  }
}
