<template>
  <button 
    :class="buttonClasses" 
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <div v-if="loading" class="loading-spinner"></div>
    <slot v-if="!loading"></slot>
  </button>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'success', 'warning', 'error'].includes(value)
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  variant: {
    type: String,
    default: 'filled',
    validator: (value) => ['filled', 'outlined', 'text'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  block: {
    type: Boolean,
    default: false
  },
  round: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['click']);

const buttonClasses = computed(() => [
  'ui-button',
  `ui-button--${props.type}`,
  `ui-button--${props.size}`,
  `ui-button--${props.variant}`,
  {
    'ui-button--disabled': props.disabled,
    'ui-button--loading': props.loading,
    'ui-button--block': props.block,
    'ui-button--round': props.round
  }
]);

function handleClick(event) {
  if (!props.disabled && !props.loading) {
    emit('click', event);
  }
}
</script>

<style lang="scss" scoped>
@import '../../styles/design-system.scss';

.ui-button {
  @include button-base;
  position: relative;
  overflow: hidden;
  
  // 尺寸变体
  &--small {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-sm;
    height: 28px;
  }
  
  &--medium {
    padding: $spacing-sm $spacing-md;
    font-size: $font-size-md;
    height: 36px;
  }
  
  &--large {
    padding: $spacing-md $spacing-lg;
    font-size: $font-size-lg;
    height: 44px;
  }
  
  // 类型变体
  &--default {
    background: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    
    &:hover:not(.ui-button--disabled) {
      background: var(--bg-tertiary);
      border-color: var(--border-hover);
    }
  }
  
  &--primary {
    background: var(--primary-color);
    color: white;
    
    &:hover:not(.ui-button--disabled) {
      background: var(--primary-hover);
    }
    
    &:active {
      background: var(--primary-active);
    }
  }
  
  &--success {
    background: var(--success-color);
    color: white;
    
    &:hover:not(.ui-button--disabled) {
      background: #73d13d;
    }
  }
  
  &--warning {
    background: var(--warning-color);
    color: white;
    
    &:hover:not(.ui-button--disabled) {
      background: #ffc53d;
    }
  }
  
  &--error {
    background: var(--error-color);
    color: white;
    
    &:hover:not(.ui-button--disabled) {
      background: #ff7875;
    }
  }
  
  // 样式变体
  &--outlined {
    background: transparent;
    border: 1px solid currentColor;
    
    &.ui-button--primary {
      color: var(--primary-color);
      
      &:hover:not(.ui-button--disabled) {
        background: var(--primary-light);
      }
    }
  }
  
  &--text {
    background: transparent;
    border: none;
    
    &:hover:not(.ui-button--disabled) {
      background: rgba(255, 255, 255, 0.1);
    }
  }
  
  // 状态变体
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &--loading {
    cursor: not-allowed;
  }
  
  &--block {
    width: 100%;
  }
  
  &--round {
    border-radius: $border-radius-full;
  }
  
  // 加载动画
  .loading-spinner {
    width: 14px;
    height: 14px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
