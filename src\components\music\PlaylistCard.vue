<template>
  <div
    :class="cardClasses"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 歌单封面容器 -->
    <div class="playlist-cover">
      <!-- 封面图片 -->
      <img
        :src="playlist.coverUrl || defaultCover"
        :alt="playlist.title"
        class="cover-image"
        @error="handleImageError"
      />

      <!-- 顶部信息标签 -->
      <div class="top-info-tags">
        <div v-if="playlist.tag" class="playlist-tag">
          {{ playlist.tag }}
        </div>
        <div class="song-count">
          {{ playlist.trackCount || 0 }}首
        </div>
      </div>

      <!-- 底部毛玻璃信息层 -->
      <div class="glass-info-layer" :class="{ expanded: isHovered }">
        <!-- 歌曲预览内容 -->
        <div class="song-preview glass-background" :class="{ visible: isHovered }">
          <!-- 歌单标题 -->
          <div class="playlist-title">{{ playlist.title }}</div>

          <!-- 歌曲列表 -->
          <div class="song-list">
            <div
              v-for="(song, index) in previewSongs"
              :key="song.id || index"
              class="preview-song"
            >
              {{ index + 1 }}. {{ song.name }} - {{ song.artist }}
            </div>
            <div v-if="previewSongs.length === 0" class="preview-song">
              暂无预览歌曲
            </div>
          </div>
        </div>

        <!-- 播放控制按钮 -->
        <div
          class="play-button"
          :class="{ visible: isHovered }"
          @click.stop="handlePlay"
        >
          <play-circle-filled />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import {
  PlayCircleFilled
} from '@ant-design/icons-vue';

const props = defineProps({
  playlist: {
    type: Object,
    required: true
  },
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'compact', 'large'].includes(value)
  }
});

const emit = defineEmits(['click', 'play']);

// 响应式状态
const isHovered = ref(false);
const hoverTimer = ref(null);
const enableTransitions = ref(false); // 控制是否启用过渡效果

const defaultCover = '/images/default-playlist.jpg';

// 计算属性
const cardClasses = computed(() => [
  'playlist-card',
  `playlist-card--${props.variant}`,
  {
    'card-hovered': isHovered.value,
    'transitions-enabled': enableTransitions.value
  }
]);

// 获取前3首歌曲用于预览
const previewSongs = computed(() => {
  return props.playlist.previewSongs || [];
});

// 方法
function handleClick() {
  emit('click', props.playlist);
}

function handlePlay() {
  emit('play', props.playlist);
}

function handleImageError(event) {
  event.target.src = defaultCover;
}

// 鼠标事件处理 - 添加100ms延时防止滚动时卡顿
function handleMouseEnter() {
  // 清除之前的定时器
  if (hoverTimer.value) {
    clearTimeout(hoverTimer.value);
  }

  // 延时100ms触发hover效果
  hoverTimer.value = setTimeout(() => {
    enableTransitions.value = true; // 启用过渡效果
    isHovered.value = true;
  }, 100);
}

function handleMouseLeave() {
  // 清除定时器
  if (hoverTimer.value) {
    clearTimeout(hoverTimer.value);
    hoverTimer.value = null;
  }

  // 确保过渡效果已启用，然后取消hover状态
  if (isHovered.value) {
    enableTransitions.value = true; // 确保退出动画平滑
  }
  isHovered.value = false;

  // 延时禁用过渡效果，避免快速滑动时触发
  setTimeout(() => {
    if (!isHovered.value) {
      enableTransitions.value = false;
    }
  }, 500); // 等待动画完成后禁用
}
</script>

<style lang="scss" scoped>
@import '../../styles/design-system.scss';

// ===== 歌单卡片主容器 =====
.playlist-card {
  background: var(--bg-card);
  border-radius: $border-radius-lg;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  width: 160px;
  cursor: pointer;
  overflow: hidden;
  @include transition(all, 0.3s, ease-out);

  // 延时悬浮效果 - 通过JavaScript控制
  &.card-hovered {
    transform: translateY(-2px);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-md);
  }

  // 紧凑版本
  &--compact {
    width: 160px;

    .playlist-cover {
      height: 200px;
    }
  }

  // 大尺寸版本
  &--large {
    width: 240px;

    .playlist-cover {
      height: 280px;
    }
  }
}

// ===== 歌单封面容器 =====
.playlist-cover {
  position: relative;
  width: 100%;
  height: 240px;
  border-radius: $border-radius-lg;
  overflow: hidden;

  // 封面图片
  .cover-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    // 移除过渡效果，避免快速滑动时抖动
  }

  // 顶部信息标签容器
  .top-info-tags {
    position: absolute;
    top: $spacing-sm;
    left: $spacing-sm;
    right: $spacing-sm;
    display: flex;
    justify-content: space-between;
    z-index: 3;

    .playlist-tag,
    .song-count {
      padding: $spacing-xs $spacing-sm;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      font-size: $font-size-xs;
      border-radius: $border-radius-sm;
      backdrop-filter: blur(4px);
    }
  }

  // ===== 底部毛玻璃信息层 =====
  .glass-info-layer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 40px; // 默认高度，只显示歌单标题
    z-index: 10;
    transition: none; // 默认无过渡

    // 悬浮展开状态
    &.expanded {
      height: 110px; // 展开高度，显示完整歌曲列表

      .song-preview {
        opacity: 1;
        transform: translateY(0);
      }

      .play-button {
        opacity: 1;
        transform: translateY(0);
      }
    }

    // 歌曲预览内容（包含毛玻璃背景）
    .song-preview {
      position: relative;
      padding: 10px 12px;
      height: 150px; // 固定高度，包含所有内容
      display: flex;
      flex-direction: column;
      justify-content: flex-end; // 内容靠底部对齐
      color: white;
      opacity: 0.9; // 默认透明度
      transition: none; // 默认无过渡

      // 悬浮时完全显示
      &.visible {
        opacity: 1;
      }

      // 毛玻璃背景效果
      &.glass-background {
        background: linear-gradient(
          to top,
          rgba(0, 0, 0, 0.8) 0%,
          rgba(0, 0, 0, 0.6) 50%,
          rgba(0, 0, 0, 0.3) 80%,
          rgba(0, 0, 0, 0.1) 100%
        );
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        border-radius: 0 0 $border-radius-lg $border-radius-lg;
      }

      // 歌单标题
      .playlist-title {
        font-size: .75rem;
        font-weight: $font-weight-bold;
        margin: 0 0 .75rem 0;
        line-height: 1.3;
        @include text-ellipsis-multiline(2);
        max-height: 2.6em;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      // 歌曲列表容器
      .song-list {
        flex: 1;

        .preview-song {
          font-size: $font-size-xs;
          line-height: 1.3;
          margin-bottom: $spacing-xs;
          opacity: 0.9;
          @include text-ellipsis;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    // ===== 播放控制按钮 =====
    .play-button {
      position: absolute;
      bottom: $spacing-sm;
      right: $spacing-sm;
      width: 32px;
      height: 32px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      @include flex-center;
      color: var(--primary-color);
      font-size: 16px;
      z-index: 12;
      cursor: pointer;

      // 默认隐藏状态
      opacity: 0;
      transform: translateY(8px);
      backdrop-filter: blur(10px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      transition: none; // 默认无过渡

      // 悬浮时显示
      &.visible {
        opacity: 1;
        transform: translateY(0);

        // 按钮悬浮效果 - 移除:hover，避免滚动时触发
        &:hover {
          transform: translateY(0) scale(1.1);
          background: white;
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
          transition: all 0.2s ease-out; // 悬浮时的快速过渡
        }
      }
    }
  }
}

// ===== 过渡效果控制 =====
.playlist-card.transitions-enabled {
  .glass-info-layer {
    transition: height 0.4s ease-out;
  }

  .song-preview {
    transition: opacity 0.4s ease-out, transform 0.4s ease-out;
  }

  .play-button {
    transition: opacity 0.3s ease-out, transform 0.3s ease-out;
  }
}

// ===== 移动端响应式设计 =====
@include respond-to(xs) {
  .playlist-card {
    width: 140px;

    // 紧凑版本
    &--compact {
      width: 120px;

      .playlist-cover {
        height: 200px;
      }
    }

    // 大尺寸版本
    &--large {
      width: 180px;

      .playlist-cover {
        height: 240px;
      }
    }

    // 封面容器移动端适配
    .playlist-cover {
      height: 200px;

      // 毛玻璃信息层移动端适配
      .glass-info-layer {
        height: 35px; // 移动端默认高度

        &.expanded {
          height: 100px; // 移动端展开高度
        }

        // 歌曲预览移动端适配
        .song-preview {
          padding: $spacing-sm;
          height: 120px;

          .playlist-title {
            font-size: $font-size-sm;
            margin-bottom: 8px;
            max-height: 2.2em;
          }

          .song-list {
            .preview-song {
              font-size: 10px;
              line-height: 1.2;
              margin-bottom: 4px;
            }
          }
        }

        // 播放按钮移动端适配
        .play-button {
          bottom: 6px;
          right: 6px;
          width: 28px;
          height: 28px;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
