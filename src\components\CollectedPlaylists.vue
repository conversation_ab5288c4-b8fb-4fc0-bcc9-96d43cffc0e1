<template>
  <div class="collected-playlists">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="playlist-info">
        <div class="playlist-cover" @click="createNewPlaylist">
          <img :src="coverImage" alt="我收藏的歌单" />
          <div class="play-overlay">
            <play-circle-filled class="play-icon" />
          </div>
        </div>
        <div class="playlist-details">
          <h1 class="playlist-title">我收藏的歌单</h1>
          <div class="playlist-meta">
            <a-avatar size="small" class="creator-avatar">
              <template #icon><user-outlined /></template>
            </a-avatar>
            <span class="creator-name">{{ currentUser?.username || 'Guest' }}</span>
            <span class="create-time">2025-6-4创建</span>
            <span class="separator">•</span>
            <span class="playlist-count">{{ filteredPlaylists.length }}个歌单</span>
            <span class="separator">•</span>
            <span class="play-count">播放 {{ totalPlayCount }}</span>
            <span class="separator">•</span>
            <span class="collect-count">收藏 {{ totalCollectCount }}</span>
          </div>
          <div class="playlist-actions">
            <a-button type="primary" class="create-playlist-btn" @click="createNewPlaylist">
              <plus-outlined />
              新建歌单
            </a-button>
            <a-button class="action-btn" @click="openPlaylistManager">
              <setting-outlined />
              歌单管理
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="content-section">
      <div class="section-header">
        <div class="header-tabs">
          <span class="tab" :class="{ active: activeTab === 'collected' }" @click="setActiveTab('collected')">收藏</span>
          <span class="tab" :class="{ active: activeTab === 'created' }" @click="setActiveTab('created')">自建</span>
        </div>
        <div class="search-box">
          <a-input
            placeholder="搜索歌单"
            size="small"
            class="search-input"
            v-model:value="searchQuery"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
        </div>
      </div>

      <!-- 歌单网格 -->
      <div class="playlists-grid">
        <PlaylistCard
          v-for="playlist in filteredPlaylists"
          :key="playlist.id"
          :playlist="playlist"
          variant="compact"
          @click="openPlaylistDetail"
          @play="playPlaylist"
        />
      </div>

      <!-- 空状态 -->
      <div v-if="filteredPlaylists.length === 0" class="empty-state">
        <inbox-outlined class="empty-icon" />
        <p class="empty-text">{{ searchQuery ? '没有找到相关歌单' : '还没有收藏任何歌单' }}</p>
      </div>
    </div>

    <!-- 歌单管理模态框 -->
    <a-modal
      v-model:open="showPlaylistManager"
      title="歌单管理"
      width="1000px"
      :footer="null"
      class="playlist-manager-modal"
    >
      <div class="playlist-manager">
        <!-- 操作栏 -->
        <div class="manager-toolbar">
          <div class="toolbar-left">
            <a-checkbox
              v-model:checked="selectAll"
              @change="handleSelectAll"
              :indeterminate="indeterminate"
            >
              全选
            </a-checkbox>
            <span class="selected-count">已选择 {{ selectedPlaylists.length }} 个歌单</span>
          </div>
          <div class="toolbar-right">
            <a-button
              type="primary"
              danger
              size="small"
              :disabled="selectedPlaylists.length === 0"
              @click="deleteSelectedPlaylists"
            >
              <delete-outlined />
              删除选中
            </a-button>
            <a-button
              size="small"
              :disabled="deletedPlaylists.length === 0"
              @click="restoreAllPlaylists"
            >
              <redo-outlined />
              恢复全部
            </a-button>
          </div>
        </div>

        <!-- 歌单卡片网格 -->
        <div class="playlist-manager-grid">
          <div
            v-for="playlist in allPlaylistsForTable"
            :key="playlist.id"
            class="selectable-playlist-wrapper"
            :class="{
              selected: selectedPlaylists.includes(playlist.id),
              deleted: playlist.isDeleted
            }"
            @click="togglePlaylistSelection(playlist)"
          >
            <!-- 选择框 -->
            <div class="selection-checkbox" @click.stop>
              <a-checkbox
                :checked="selectedPlaylists.includes(playlist.id)"
                :disabled="playlist.isDeleted"
                @change="(e) => handleSingleSelection(playlist.id, e.target.checked)"
              />
            </div>

            <!-- 复用歌单卡片组件 -->
            <PlaylistCard
              :playlist="playlist"
              variant="compact"
              @click="() => {}"
              @play="() => {}"
              class="manager-playlist-card"
              :class="{ disabled: playlist.isDeleted }"
            />

            <!-- 状态标签 -->
            <div v-if="playlist.isDeleted" class="deleted-badge">
              已删除
            </div>

            <!-- 操作按钮 -->
            <div class="card-actions" @click.stop>
              <template v-if="!playlist.isDeleted">
                <a-button size="small" type="text" @click="editPlaylist(playlist)" title="编辑">
                  <edit-outlined />
                </a-button>
                <a-button size="small" type="text" danger @click="deletePlaylist(playlist.id)" title="删除">
                  <delete-outlined />
                </a-button>
              </template>
              <template v-else>
                <a-button size="small" type="text" @click="restorePlaylist(playlist.id)" title="恢复">
                  <redo-outlined />
                </a-button>
                <a-popconfirm
                  title="确定要永久删除这个歌单吗？"
                  @confirm="permanentDeletePlaylist(playlist.id)"
                >
                  <a-button size="small" type="text" danger title="永久删除">
                    <delete-outlined />
                  </a-button>
                </a-popconfirm>
              </template>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useMusicStore } from '../stores/music';
import PlaylistCard from './music/PlaylistCard.vue';
import {
  PlayCircleFilled,
  PlusOutlined,
  UserOutlined,
  SettingOutlined,
  DeleteOutlined,
  RedoOutlined,
  EditOutlined,
  SearchOutlined,
  InboxOutlined
} from '@ant-design/icons-vue';

const musicStore = useMusicStore();
const router = useRouter();

// 接收父组件传递的用户信息
const props = defineProps({
  currentUser: {
    type: Object,
    default: null
  }
});

// 响应式数据
const searchQuery = ref('');
const coverImage = new URL('../assets/preview.jpg', import.meta.url).href;

// 标签页状态
const activeTab = ref('collected'); // 'created' | 'collected'

// 歌单管理相关
const showPlaylistManager = ref(false);
const selectedPlaylists = ref([]);
const deletedPlaylists = ref([]);
const selectAll = ref(false);
const indeterminate = ref(false);

// 我收藏的歌单列表
const collectedPlaylists = ref([
  {
    id: 1,
    title: '流行经典',
    description: '经典流行歌曲合集',
    coverUrl: 'https://picsum.photos/200/200?random=1',
    tag: '流行',
    songs: [
      { id: 1, title: 'Love Story', artist: 'Taylor Swift' },
      { id: 2, title: 'Shape of You', artist: 'Ed Sheeran' },
      { id: 3, title: 'Blinding Lights', artist: 'The Weeknd' }
    ]
  },
  {
    id: 2,
    title: '摇滚精选',
    description: '经典摇滚音乐',
    coverUrl: 'https://picsum.photos/200/200?random=2',
    tag: '摇滚',
    songs: [
      { id: 4, title: 'Bohemian Rhapsody', artist: 'Queen' },
      { id: 5, title: 'Hotel California', artist: 'Eagles' },
      { id: 6, title: 'Stairway to Heaven', artist: 'Led Zeppelin' }
    ]
  },
  {
    id: 3,
    title: '电子音乐',
    description: '现代电子音乐合集',
    coverUrl: 'https://picsum.photos/200/200?random=3',
    tag: '电子',
    songs: [
      { id: 7, title: 'Levels', artist: 'Avicii' },
      { id: 8, title: 'Titanium', artist: 'David Guetta' },
      { id: 9, title: 'Animals', artist: 'Martin Garrix' }
    ]
  },
  {
    id: 4,
    title: '华语金曲',
    description: '华语流行经典',
    coverUrl: 'https://picsum.photos/200/200?random=4',
    tag: '华语',
    songs: [
      { id: 10, title: '青花瓷', artist: '周杰伦' },
      { id: 11, title: '月亮代表我的心', artist: '邓丽君' },
      { id: 12, title: '十年', artist: '陈奕迅' }
    ]
  },
  {
    id: 5,
    title: '爵士乐',
    description: '经典爵士乐曲',
    coverUrl: 'https://picsum.photos/200/200?random=5',
    tag: '爵士',
    songs: [
      { id: 13, title: 'Fly Me to the Moon', artist: 'Frank Sinatra' },
      { id: 14, title: 'What a Wonderful World', artist: 'Louis Armstrong' },
      { id: 15, title: 'Summertime', artist: 'Ella Fitzgerald' }
    ]
  },
  {
    id: 6,
    title: '民谣时光',
    description: '温暖民谣歌曲',
    coverUrl: 'https://picsum.photos/200/200?random=6',
    tag: '民谣',
    songs: [
      { id: 16, title: '南山南', artist: '马頔' },
      { id: 17, title: '成都', artist: '赵雷' },
      { id: 18, title: '理想', artist: '赵雷' }
    ]
  }
]);

// 计算属性
const totalPlayCount = computed(() => '8.5w+');
const totalCollectCount = computed(() => '1875');

// 过滤后的歌单列表
const filteredPlaylists = computed(() => {
  let playlists = collectedPlaylists.value;

  // 根据标签页过滤
  if (activeTab.value === 'created') {
    // 自建歌单：显示标签为"自建"的歌单
    playlists = playlists.filter(playlist => playlist.tag === '自建');
  } else if (activeTab.value === 'collected') {
    // 收藏歌单：显示非"自建"标签的歌单
    playlists = playlists.filter(playlist => playlist.tag !== '自建');
  }

  // 搜索过滤
  if (searchQuery.value) {
    const keyword = searchQuery.value.toLowerCase();
    playlists = playlists.filter(playlist => {
      return playlist.title.toLowerCase().includes(keyword) ||
             playlist.description.toLowerCase().includes(keyword) ||
             playlist.tag.toLowerCase().includes(keyword);
    });
  }

  return playlists;
});

// 管理模态框数据源（包含正常和已删除的歌单）
const allPlaylistsForTable = computed(() => {
  const normal = collectedPlaylists.value.map(playlist => ({
    ...playlist,
    isDeleted: false
  }));

  const deleted = deletedPlaylists.value.map(playlist => ({
    ...playlist,
    isDeleted: true
  }));

  return [...normal, ...deleted];
});

// 方法
function setActiveTab(tab) {
  activeTab.value = tab;
}

function createNewPlaylist() {
  // 这里可以打开创建歌单的模态框或跳转到创建页面
  console.log('创建新歌单');
  // 示例：添加一个新歌单到列表
  const newPlaylist = {
    id: Date.now(),
    title: '我的新歌单',
    description: '新建的歌单',
    coverUrl: 'https://picsum.photos/200/200?random=' + Date.now(),
    tag: '自建',
    songs: []
  };
  collectedPlaylists.value.unshift(newPlaylist);
}

function playPlaylist(playlist) {
  if (playlist.songs && playlist.songs.length > 0) {
    musicStore.currentSong = playlist.songs[0];
  }
}

function openPlaylistDetail(playlist) {
  router.push(`/playlist/${playlist.id}`);
}

// 歌单管理方法
function openPlaylistManager() {
  showPlaylistManager.value = true;
}

function handleSelectAll(e) {
  if (e.target.checked) {
    selectedPlaylists.value = collectedPlaylists.value.map(p => p.id);
  } else {
    selectedPlaylists.value = [];
  }
  updateSelectAllState();
}

function updateSelectAllState() {
  const total = collectedPlaylists.value.length;
  const selected = selectedPlaylists.value.length;

  selectAll.value = selected === total && total > 0;
  indeterminate.value = selected > 0 && selected < total;
}

function deletePlaylist(playlistId) {
  const playlist = collectedPlaylists.value.find(p => p.id === playlistId);
  if (playlist) {
    deletedPlaylists.value.push(playlist);
    collectedPlaylists.value = collectedPlaylists.value.filter(p => p.id !== playlistId);
    selectedPlaylists.value = selectedPlaylists.value.filter(id => id !== playlistId);
    updateSelectAllState();
  }
}

function deleteSelectedPlaylists() {
  const playlistsToDelete = collectedPlaylists.value.filter(p => selectedPlaylists.value.includes(p.id));
  deletedPlaylists.value.push(...playlistsToDelete);
  collectedPlaylists.value = collectedPlaylists.value.filter(p => !selectedPlaylists.value.includes(p.id));
  selectedPlaylists.value = [];
  updateSelectAllState();
}

function restorePlaylist(playlistId) {
  const playlist = deletedPlaylists.value.find(p => p.id === playlistId);
  if (playlist) {
    collectedPlaylists.value.push(playlist);
    deletedPlaylists.value = deletedPlaylists.value.filter(p => p.id !== playlistId);
  }
}

function restoreAllPlaylists() {
  collectedPlaylists.value.push(...deletedPlaylists.value);
  deletedPlaylists.value = [];
}

function permanentDeletePlaylist(playlistId) {
  deletedPlaylists.value = deletedPlaylists.value.filter(p => p.id !== playlistId);
}

// 卡片选择相关方法
function togglePlaylistSelection(playlist) {
  if (playlist.isDeleted) return;

  const index = selectedPlaylists.value.indexOf(playlist.id);
  if (index > -1) {
    selectedPlaylists.value.splice(index, 1);
  } else {
    selectedPlaylists.value.push(playlist.id);
  }
  updateSelectAllState();
}

function handleSingleSelection(playlistId, checked) {
  if (checked) {
    if (!selectedPlaylists.value.includes(playlistId)) {
      selectedPlaylists.value.push(playlistId);
    }
  } else {
    selectedPlaylists.value = selectedPlaylists.value.filter(id => id !== playlistId);
  }
  updateSelectAllState();
}



function editPlaylist(playlist) {
  // 这里可以打开编辑歌单的模态框
  console.log('编辑歌单:', playlist);
}
</script>

<style lang="scss" scoped>
@import '../styles/design-system.scss';

// 歌单管理专用颜色变量
:root {
  --playlist-manager-primary: #1890ff;
  --playlist-manager-primary-light: #40a9ff;
  --playlist-manager-primary-bg: rgba(24, 144, 255, 0.05);
  --playlist-manager-border: rgba(24, 144, 255, 0.1);
  --playlist-manager-shadow: rgba(24, 144, 255, 0.15);
}

.collected-playlists {
  height: 100%;
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  color: var(--text-primary);
  overflow-y: scroll; // 强制显示滚动条，避免内容变化时的抖动
  overflow-x: hidden;
  @include custom-scrollbar(); // 使用自定义滚动条样式
  padding-bottom: var(--player-controls-height);

  .page-header {
    padding: $spacing-xl;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(10px);

    .playlist-info {
      display: flex;
      gap: $spacing-lg;

      .playlist-cover {
        position: relative;
        width: 200px;
        height: 200px;
        border-radius: $border-radius-lg;
        overflow: hidden;
        cursor: pointer;
        @include hover-lift;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .play-overlay {
          position: absolute;
          inset: 0;
          background: var(--bg-overlay);
          @include flex-center;
          opacity: 0;
          @include transition(opacity);

          .play-icon {
            font-size: 60px;
            color: var(--text-primary);
            cursor: pointer;
          }
        }

        &:hover .play-overlay {
          opacity: 1;
        }
      }

      .playlist-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .playlist-title {
          font-size: $font-size-xxxl;
          font-weight: $font-weight-bold;
          margin-bottom: $spacing-md;
        }

        .playlist-meta {
          display: flex;
          align-items: center;
          gap: $spacing-sm;
          margin-bottom: $spacing-lg;
          font-size: $font-size-md;
          flex-wrap: wrap;

          .creator-avatar {
            background-color: var(--bg-card);
          }

          .creator-name {
            color: var(--primary-color);
            cursor: pointer;
            font-weight: $font-weight-medium;
            @include transition(color);

            &:hover {
              color: var(--primary-hover);
            }
          }

          .create-time {
            color: var(--text-secondary);
            opacity: 0.8;
          }

          .separator {
            color: var(--text-secondary);
            opacity: 0.5;
            margin: 0 6px;
          }

          .playlist-count {
            color: var(--text-primary);
            font-weight: $font-weight-medium;
            opacity: 0.9;
          }

          .play-count,
          .collect-count {
            color: var(--primary-color);
            font-weight: $font-weight-medium;
            opacity: 1;
          }
        }

        .playlist-actions {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: $spacing-md;

          .create-playlist-btn {
            background: #ec4141;
            border: none;
            border-radius: $spacing-lg;
            padding: 12px 32px;
            font-size: $font-size-lg;
            font-weight: $font-weight-medium;
            height: 48px;
            min-width: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: $spacing-sm;
            @include transition();

            &:hover {
              background: #d73027;
              transform: translateY(-1px);
            }
          }

          .action-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            border-radius: $spacing-lg;
            padding: 12px 20px;
            font-size: $font-size-md;
            height: 48px;
            min-width: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            @include transition();

            &:hover {
              background: rgba(255, 255, 255, 0.2);
              border-color: var(--border-hover);
              transform: translateY(-1px);
            }
          }
        }
      }
    }
  }

  .content-section {
    padding: 0 $spacing-xl;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-lg;
      border-bottom: 1px solid var(--border-color);
      padding: $spacing-md;

      .header-tabs {
        display: flex;
        gap: $spacing-xxl;

        .tab {
          font-size: $font-size-lg;
          cursor: pointer;
          padding-bottom: $spacing-sm;
          opacity: 0.6;
          @include transition(opacity);

          &.active {
            opacity: 1;
            border-bottom: 2px solid #ec4141;
          }

          &:hover {
            opacity: 0.8;
          }
        }
      }

      .search-box {
        .search-input {
          width: 240px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid var(--border-color);
          border-radius: $spacing-md;

          :deep(.ant-input) {
            background: transparent;
            color: var(--text-primary);
            border: none;
            padding: 8px 12px;

            &::placeholder {
              color: var(--text-secondary);
            }
          }

          :deep(.ant-input-prefix) {
            color: var(--text-secondary);
            margin-right: 8px;
          }
        }
      }
    }

    .playlists-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
      gap: $spacing-lg;
      margin-bottom: $spacing-xl;

      @include respond-to(xs) {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: $spacing-md;
      }
    }

    .empty-state {
      @include flex-center;
      flex-direction: column;
      padding: $spacing-xxl;
      opacity: 0.8;
      background: linear-gradient(135deg,
        rgba(24, 144, 255, 0.03) 0%,
        rgba(24, 144, 255, 0.01) 100%);
      border-radius: $border-radius-xl;
      margin: $spacing-xl;
      border: 2px dashed rgba(24, 144, 255, 0.2);
      min-height: 300px;

      .empty-icon {
        font-size: 64px;
        color: var(--primary-color);
        margin-bottom: $spacing-lg;
        opacity: 0.7;
        @include transition(all, 0.3s, ease);

        &:hover {
          transform: scale(1.1);
          opacity: 1;
        }
      }

      .empty-text {
        font-size: $font-size-lg;
        color: var(--text-primary);
        margin: 0;
        font-weight: $font-weight-medium;
        text-align: center;
        line-height: 1.6;
      }
    }
  }

  // 歌单管理模态框样式
  :deep(.playlist-manager-modal) {
    .ant-modal-content {
      background: var(--bg-card);
      border-radius: $border-radius-xl;
      overflow: hidden;
      box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.1);
      @include transition(all, 0.3s, ease);

      &:hover {
        box-shadow:
          0 25px 80px rgba(0, 0, 0, 0.2),
          0 0 0 1px rgba(255, 255, 255, 0.15);
      }
    }

    .ant-modal-header {
      background: linear-gradient(135deg,
        var(--primary-color) 0%,
        var(--primary-color-light) 50%,
        #40a9ff 100%);
      border-bottom: none;
      padding: $spacing-xl $spacing-xxl;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg,
          transparent 0%,
          rgba(255, 255, 255, 0.3) 50%,
          transparent 100%);
      }
    }

    .ant-modal-title {
      color: white;
      font-size: $font-size-xl;
      font-weight: $font-weight-bold;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      display: flex;
      align-items: center;
      gap: $spacing-md;

      &::before {
        content: '🎵';
        font-size: $font-size-lg;
      }
    }
  }

  .playlist-manager {
    .manager-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: $spacing-lg $spacing-xl;
      background: linear-gradient(135deg,
        rgba(24, 144, 255, 0.05) 0%,
        rgba(24, 144, 255, 0.02) 100%);
      border-bottom: 1px solid rgba(24, 144, 255, 0.1);
      margin-bottom: $spacing-xl;
      border-radius: $border-radius-lg $border-radius-lg 0 0;

      .toolbar-left {
        display: flex;
        align-items: center;
        gap: $spacing-lg;

        .selected-count {
          color: var(--primary-color);
          font-size: $font-size-md;
          font-weight: $font-weight-medium;
          padding: $spacing-xs $spacing-md;
          background: rgba(24, 144, 255, 0.1);
          border-radius: $border-radius-lg;
          border: 1px solid rgba(24, 144, 255, 0.2);
        }
      }

      .toolbar-right {
        display: flex;
        gap: $spacing-md;

        .ant-btn {
          border-radius: $border-radius-lg;
          font-weight: $font-weight-medium;
          @include transition(all, 0.2s, ease);

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }
        }
      }
    }

    // 表格样式
    :deep(.playlist-table) {
      // 歌单卡片网格
      .playlist-manager-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: $spacing-xl;
        max-height: 600px;
        overflow-y: auto;
        padding: $spacing-lg;
        @include custom-scrollbar();

        // 确保至少3列布局
        @media (min-width: 768px) {
          grid-template-columns: repeat(3, 1fr);
        }

        @media (min-width: 1200px) {
          grid-template-columns: repeat(4, 1fr);
        }
      }

      // 可选择的歌单卡片包装器
      .selectable-playlist-wrapper {
        position: relative;
        border: 3px solid transparent;
        border-radius: $border-radius-xl;
        background: var(--bg-card);
        @include transition(all, 0.3s, ease);
        @include hover-lift;
        overflow: hidden;
        cursor: pointer;

        &:hover {
          border-color: var(--primary-color);
          transform: translateY(-4px);
          box-shadow:
            0 8px 25px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(24, 144, 255, 0.1);
        }

        &.selected {
          border-color: var(--primary-color);
          background: linear-gradient(135deg,
            rgba(24, 144, 255, 0.08) 0%,
            rgba(24, 144, 255, 0.03) 100%);
          box-shadow:
            0 0 0 3px rgba(24, 144, 255, 0.2),
            0 8px 25px rgba(24, 144, 255, 0.15);
          transform: translateY(-2px);
        }

        &.deleted {
          opacity: 0.7;
          background: var(--bg-secondary);

          &:hover {
            border-color: var(--warning-color);
            box-shadow:
              0 8px 25px rgba(0, 0, 0, 0.1),
              0 0 0 1px rgba(255, 193, 7, 0.2);
          }

          &.selected {
            border-color: var(--warning-color);
            background: linear-gradient(135deg,
              rgba(255, 193, 7, 0.08) 0%,
              rgba(255, 193, 7, 0.03) 100%);
          }
        }

        // 选择框
        .selection-checkbox {
          position: absolute;
          top: $spacing-md;
          left: $spacing-md;
          z-index: 10;
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(10px);
          border-radius: 50%;
          padding: $spacing-sm;
          @include transition(all, 0.2s, ease);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

          &:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }

          :deep(.ant-checkbox) {
            .ant-checkbox-inner {
              border-radius: 50%;
              border-width: 2px;
              width: 18px;
              height: 18px;
            }

            &.ant-checkbox-checked .ant-checkbox-inner {
              background-color: var(--primary-color);
              border-color: var(--primary-color);
            }
          }
        }

        // 歌单卡片
        .manager-playlist-card {
          pointer-events: none; // 禁用卡片本身的点击事件

          &.disabled {
            filter: grayscale(50%);
          }

          // 完全隐藏播放按钮
          :deep(.play-button) {
            display: none !important;
          }

          // 禁用悬浮效果
          :deep(.glass-info-layer) {
            pointer-events: none;

            &.expanded {
              height: 40px !important; // 保持默认高度，不展开
            }
          }

          // 禁用悬浮状态
          &:hover :deep(.glass-info-layer) {
            height: 40px !important;

            .song-preview {
              opacity: 0.9 !important;
              transform: none !important;
            }
          }
        }

        // 已删除标签
        .deleted-badge {
          position: absolute;
          top: $spacing-md;
          right: $spacing-md;
          background: linear-gradient(135deg, var(--warning-color), #ff8c00);
          color: white;
          padding: $spacing-xs $spacing-sm;
          border-radius: $border-radius-lg;
          font-size: $font-size-xs;
          font-weight: $font-weight-bold;
          z-index: 10;
          box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
          @include transition(all, 0.2s, ease);

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
          }
        }

        // 操作按钮
        .card-actions {
          position: absolute;
          bottom: $spacing-md;
          right: $spacing-md;
          display: flex;
          gap: $spacing-xs;
          opacity: 0;
          @include transition(all, 0.3s, ease);
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(10px);
          border-radius: $border-radius-lg;
          padding: $spacing-sm;
          box-shadow:
            0 4px 20px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.2);
          transform: translateY(10px);

          .ant-btn {
            width: 32px;
            height: 32px;
            padding: 0;
            @include flex-center;
            border: none;
            border-radius: 50%;
            @include transition(all, 0.2s, ease);

            &:hover {
              background: var(--primary-color);
              color: white;
              transform: scale(1.1);
            }

            &.delete-btn:hover {
              background: var(--error-color);
            }

            &.restore-btn:hover {
              background: var(--success-color);
            }
          }
        }

        &:hover .card-actions {
          opacity: 1;
          transform: translateY(0);
        }
      }
    }
  }
}
</style>