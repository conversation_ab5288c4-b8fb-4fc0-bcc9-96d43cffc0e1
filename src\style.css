/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 隐藏所有滚动条 */
::-webkit-scrollbar {
  display: none;
}

/* 使用scrollbar-width属性隐藏Firefox的滚动条 */
* {
  scrollbar-width: none;
  -ms-overflow-style: none;
  /* IE and Edge */
}

/* 全局样式 - 与设计系统保持一致 */
:root {
  /* 兼容旧变量 */
  --primary-color: #3a4049;
  --secondary-color: #3e4552;
  --text-color: #ffffff;
  --text-secondary: #aaaaaa;
  --accent-color: #1890ff;
  --sidebar-width: 200px;

  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #282c34;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: Arial, sans-serif;
  background-color: var(--primary-color);
  color: var(--text-color);
  line-height: 1.5;
  margin: 0;
  padding: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  /* 防止body出现滚动条 */
}

html,
body,
#app {
  height: 100%;
  width: 100%;
}

#app {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}

button:hover {
  border-color: #646cff;
}

button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }

  a:hover {
    color: #747bff;
  }

  button {
    background-color: #f9f9f9;
  }
}

/* 自定义 Ant Design 组件样式 */
.ant-slider-rail {
  background-color: rgba(255, 255, 255, 0.2);
}

.ant-slider-track {
  background-color: var(--accent-color);
}

.ant-slider-handle {
  border-color: var(--accent-color);
}

.ant-input-search {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
}

.ant-input-search input {
  background-color: transparent;
  color: var(--text-color);
}

.ant-input-search .ant-input-search-button {
  background-color: transparent;
  border: none;
  color: var(--text-color);
}

.ant-badge-status-dot {
  background-color: #52c41a;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.flex-1 {
  flex: 1;
}

.gap-2 {
  gap: 0.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}