<template>
  <div class="progress-controls">
    <span class="time-display">{{ formatTime(currentTime) }}</span>
    <a-slider 
      class="progress-slider" 
      :value="progress" 
      @change="handleProgressChange" 
      :tooltip-visible="false" 
    />
    <span class="time-display">{{ formatTime(duration) }}</span>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// 接收属性
const props = defineProps({
  currentTime: {
    type: Number,
    default: 0
  },
  duration: {
    type: Number,
    default: 0
  },
  progress: {
    type: Number,
    default: 0
  }
});

// 定义事件
const emit = defineEmits(['update:progress', 'seek']);

// 处理进度条变化
function handleProgressChange(newProgress) {
  emit('update:progress', newProgress);
  emit('seek', newProgress);
}

// 格式化时间为 MM:SS 格式
function formatTime(seconds) {
  if (!seconds) return '00:10';
  
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}
</script>

<style lang="scss" scoped>
.progress-controls {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;

  .time-display {
    color: #aaa;
    font-size: 11px;
    width: 36px;
    text-align: center;
  }

  .progress-slider {
    flex: 1;

    :deep(.ant-slider-rail) {
      background-color: rgba(255, 255, 255, 0.1);
    }

    :deep(.ant-slider-track) {
      background-color: #1890ff;
    }

    :deep(.ant-slider-handle) {
      border-color: #1890ff;
    }
  }
}
</style> 