<template>
  <div :class="cardClasses" @click="handlePlay">
    <!-- 封面图片 -->
    <div class="song-cover">
      <img 
        :src="song.coverUrl || defaultCover" 
        :alt="song.title"
        @error="handleImageError"
      />
      <div class="play-overlay">
        <div class="play-button">
          <pause-outlined v-if="isCurrentSong && isPlaying" />
          <caret-right-outlined v-else />
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-overlay">
        <UiLoading type="spinner" size="small" />
      </div>
    </div>
    
    <!-- 歌曲信息 -->
    <div class="song-info">
      <h3 class="song-title" :title="song.title">{{ song.title }}</h3>
      <p class="song-artist" :title="song.artist">{{ song.artist }}</p>
      
      <!-- 标签 -->
      <div v-if="song.tags && song.tags.length" class="song-tags">
        <span 
          v-for="tag in song.tags.slice(0, 2)" 
          :key="tag" 
          class="tag"
        >
          {{ tag }}
        </span>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="song-actions">
      <div class="action-button" @click.stop="toggleLike">
        <heart-filled v-if="song.liked" class="liked" />
        <heart-outlined v-else />
      </div>
      
      <div class="action-button" @click.stop="handleDownload">
        <download-outlined />
      </div>
      
      <div class="action-button" @click.stop="handleMore">
        <more-outlined />
      </div>
      
      <!-- 时长 -->
      <span class="song-duration">{{ formatDuration(song.duration) }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import { useMusicStore } from '../../stores/music';
import {
  CaretRightOutlined,
  PauseOutlined,
  HeartFilled,
  HeartOutlined,
  DownloadOutlined,
  MoreOutlined
} from '@ant-design/icons-vue';
import UiLoading from '../ui/UiLoading.vue';

const props = defineProps({
  song: {
    type: Object,
    required: true
  },
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'compact', 'detailed'].includes(value)
  },
  showActions: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['play', 'like', 'download', 'more']);

const musicStore = useMusicStore();
const loading = ref(false);
const defaultCover = '/images/default-cover.jpg';

// 计算属性
const cardClasses = computed(() => [
  'song-card',
  `song-card--${props.variant}`,
  {
    'song-card--playing': isCurrentSong.value && isPlaying.value,
    'song-card--current': isCurrentSong.value
  }
]);

const isCurrentSong = computed(() => {
  return musicStore.currentSong?.id === props.song.id;
});

const isPlaying = computed(() => {
  return musicStore.isPlaying;
});

// 方法
function handlePlay() {
  if (isCurrentSong.value) {
    musicStore.togglePlay();
  } else {
    loading.value = true;
    // 模拟加载延迟
    setTimeout(() => {
      musicStore.playSong(props.song);
      loading.value = false;
      emit('play', props.song);
    }, 300);
  }
}

function toggleLike() {
  musicStore.toggleLike(props.song.id);
  emit('like', props.song);
}

function handleDownload() {
  emit('download', props.song);
}

function handleMore() {
  emit('more', props.song);
}

function handleImageError(event) {
  event.target.src = defaultCover;
}

function formatDuration(duration) {
  if (!duration) return '00:00';
  
  // 如果已经是格式化的字符串，直接返回
  if (typeof duration === 'string' && duration.includes(':')) {
    return duration;
  }
  
  // 如果是秒数，转换为 MM:SS 格式
  const minutes = Math.floor(duration / 60);
  const seconds = Math.floor(duration % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}
</script>

<style lang="scss" scoped>
@import '../../styles/design-system.scss';

.song-card {
  @include card;
  display: flex;
  align-items: center;
  gap: $spacing-md;
  padding: $spacing-sm;
  cursor: pointer;
  position: relative;
  
  &:hover {
    .play-overlay {
      opacity: 1;
    }
    
    .song-actions {
      opacity: 1;
    }
  }
  
  &--playing {
    border-color: var(--primary-color);
    background: rgba(24, 144, 255, 0.05);
  }
  
  &--current {
    .song-title {
      color: var(--primary-color);
    }
  }
  
  &--compact {
    padding: $spacing-xs $spacing-sm;
    
    .song-cover {
      width: 40px;
      height: 40px;
    }
    
    .song-info {
      .song-title {
        font-size: $font-size-sm;
      }
      
      .song-artist {
        font-size: $font-size-xs;
      }
    }
  }
  
  &--detailed {
    flex-direction: column;
    text-align: center;
    width: 200px;
    
    .song-cover {
      width: 160px;
      height: 160px;
      margin-bottom: $spacing-sm;
    }
    
    .song-actions {
      justify-content: center;
      margin-top: $spacing-sm;
    }
  }
}

.song-cover {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: $border-radius-sm;
  overflow: hidden;
  flex-shrink: 0;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    @include transition();
  }
  
  .play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    @include flex-center;
    opacity: 0;
    @include transition();
    
    .play-button {
      width: 24px;
      height: 24px;
      background: var(--primary-color);
      border-radius: 50%;
      @include flex-center;
      color: white;
      font-size: 12px;
      @include transition();
      
      &:hover {
        transform: scale(1.1);
      }
    }
  }
  
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    @include flex-center;
  }
}

.song-info {
  flex: 1;
  min-width: 0;
  
  .song-title {
    @include text-ellipsis;
    font-size: $font-size-md;
    font-weight: $font-weight-medium;
    color: var(--text-primary);
    margin: 0 0 $spacing-xs 0;
    line-height: 1.2;
  }
  
  .song-artist {
    @include text-ellipsis;
    font-size: $font-size-sm;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.2;
  }
  
  .song-tags {
    display: flex;
    gap: $spacing-xs;
    margin-top: $spacing-xs;
    
    .tag {
      padding: 2px $spacing-xs;
      background: rgba(255, 255, 255, 0.1);
      border-radius: $border-radius-sm;
      font-size: $font-size-xs;
      color: var(--text-secondary);
    }
  }
}

.song-actions {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  opacity: 0;
  @include transition();
  
  .action-button {
    width: 24px;
    height: 24px;
    @include flex-center;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: $border-radius-sm;
    @include transition();
    
    &:hover {
      color: var(--text-primary);
      background: rgba(255, 255, 255, 0.1);
    }
    
    .liked {
      color: var(--error-color);
    }
  }
  
  .song-duration {
    font-size: $font-size-xs;
    color: var(--text-secondary);
    min-width: 40px;
    text-align: right;
  }
}

// 响应式设计
@include respond-to(xs) {
  .song-card {
    &--detailed {
      width: 150px;
      
      .song-cover {
        width: 120px;
        height: 120px;
      }
    }
  }
}
</style>
